---
title: Payments API
description: API endpoints for payment processing, subscriptions, and billing management
---

# Payments API

API endpoints for payment processing, subscriptions, and billing management

The Payments API handles subscription creation, payment processing, billing management, and Stripe webhook integration.

## Base URL

```
/api/payments
```

## Endpoints

### Create Checkout Session

Create a Stripe checkout session for subscription or one-time payment.

**Endpoint:** `POST /api/payments/create-checkout`

**Request Body:**
```json
{
  "priceId": "price_1234567890",
  "mode": "subscription",
  "successUrl": "https://yourdomain.com/success",
  "cancelUrl": "https://yourdomain.com/cancel",
  "customerId": "cus_1234567890",
  "allowPromotionCodes": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "cs_test_1234567890",
    "url": "https://checkout.stripe.com/pay/cs_test_1234567890"
  }
}
```

### Create Customer Portal

Create a Stripe customer portal session for billing management.

**Endpoint:** `POST /api/payments/create-portal`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "returnUrl": "https://yourdomain.com/dashboard/billing"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "url": "https://billing.stripe.com/session/1234567890"
  }
}
```

### Get User Subscriptions

Retrieve current user's subscriptions.

**Endpoint:** `GET /api/payments/subscriptions`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": "sub_1234567890",
        "status": "active",
        "plan": {
          "id": "standard",
          "name": "Standard Plan",
          "price": 99,
          "interval": "month"
        },
        "currentPeriodStart": "2024-01-01T00:00:00.000Z",
        "currentPeriodEnd": "2024-02-01T00:00:00.000Z",
        "cancelAtPeriodEnd": false
      }
    ]
  }
}
```

### Cancel Subscription

Cancel a user's subscription.

**Endpoint:** `POST /api/payments/cancel`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "subscriptionId": "sub_1234567890",
  "cancelAtPeriodEnd": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "sub_1234567890",
      "status": "active",
      "cancelAtPeriodEnd": true,
      "cancelAt": "2024-02-01T00:00:00.000Z"
    }
  }
}
```

### Update Subscription

Update subscription plan or quantity.

**Endpoint:** `PUT /api/payments/subscriptions/[id]`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "priceId": "price_new_plan",
  "quantity": 1,
  "prorationBehavior": "create_prorations"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "sub_1234567890",
      "status": "active",
      "plan": {
        "id": "premium",
        "name": "Premium Plan",
        "price": 299
      }
    }
  }
}
```

### Get Payment History

Retrieve user's payment history.

**Endpoint:** `GET /api/payments/history`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Query Parameters:**
- `limit` - Number of payments to return (default: 20)
- `startingAfter` - Pagination cursor

**Response:**
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "id": "pi_1234567890",
        "amount": 9900,
        "currency": "usd",
        "status": "succeeded",
        "created": "2024-01-01T00:00:00.000Z",
        "description": "Subscription payment"
      }
    ],
    "hasMore": false
  }
}
```

### Apply Coupon

Apply a coupon to a subscription.

**Endpoint:** `POST /api/payments/apply-coupon`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "subscriptionId": "sub_1234567890",
  "couponId": "SAVE20"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "sub_1234567890",
      "discount": {
        "coupon": {
          "id": "SAVE20",
          "percentOff": 20
        }
      }
    }
  }
}
```

### Get Usage Records

Retrieve usage records for metered billing.

**Endpoint:** `GET /api/payments/usage`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Query Parameters:**
- `subscriptionId` - Subscription ID
- `startDate` - Start date for usage period
- `endDate` - End date for usage period

**Response:**
```json
{
  "success": true,
  "data": {
    "usage": [
      {
        "id": "mbur_1234567890",
        "quantity": 100,
        "timestamp": "2024-01-01T00:00:00.000Z",
        "subscriptionItem": "si_1234567890"
      }
    ],
    "totalUsage": 100
  }
}
```

### Record Usage

Record usage for metered billing.

**Endpoint:** `POST /api/payments/usage`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Request Body:**
```json
{
  "subscriptionId": "sub_1234567890",
  "quantity": 10,
  "action": "increment"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "usageRecord": {
      "id": "mbur_1234567890",
      "quantity": 10,
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## Webhook Endpoints

### Stripe Webhook

Handle Stripe webhook events.

**Endpoint:** `POST /api/stripe/webhook`

**Headers:**
```
Stripe-Signature: <webhook_signature>
```

**Supported Events:**
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `customer.created`
- `customer.updated`

**Response:**
```json
{
  "received": true
}
```

## Error Codes

### Payment Errors

- `PAYMENT_FAILED` - Payment processing failed
- `CARD_DECLINED` - Credit card was declined
- `INSUFFICIENT_FUNDS` - Insufficient funds
- `EXPIRED_CARD` - Credit card has expired
- `INVALID_CARD` - Invalid card details

### Subscription Errors

- `SUBSCRIPTION_NOT_FOUND` - Subscription does not exist
- `SUBSCRIPTION_CANCELLED` - Subscription is already cancelled
- `PLAN_NOT_FOUND` - Pricing plan not found
- `INVALID_PRORATION` - Invalid proration settings

### Customer Errors

- `CUSTOMER_NOT_FOUND` - Customer does not exist
- `INVALID_CUSTOMER` - Customer data is invalid
- `CUSTOMER_DELINQUENT` - Customer has unpaid invoices

## Rate Limiting

Payment endpoints have specific rate limits:

- **Checkout creation**: 10 per minute per user
- **Portal access**: 5 per minute per user
- **Subscription changes**: 3 per minute per user
- **Usage recording**: 100 per minute per user

## Security

### Webhook Security

Verify webhook signatures:

```typescript
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;

  try {
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
    
    // Process event
    return new Response('OK');
  } catch (error) {
    return new Response('Invalid signature', { status: 400 });
  }
}
```

### Payment Security

- All payment data is processed by Stripe
- No sensitive payment information is stored
- PCI compliance handled by Stripe
- Secure webhook signature verification

## Examples

### Complete Checkout Flow

```typescript
// Create checkout session
async function createCheckout(priceId: string) {
  const response = await fetch('/api/payments/create-checkout', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      priceId,
      mode: 'subscription',
      successUrl: `${window.location.origin}/success`,
      cancelUrl: `${window.location.origin}/cancel`
    })
  });

  const data = await response.json();
  
  if (data.success) {
    // Redirect to Stripe Checkout
    window.location.href = data.data.url;
  }
}
```

### Subscription Management

```typescript
// Cancel subscription
async function cancelSubscription(subscriptionId: string) {
  const response = await fetch('/api/payments/cancel', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      subscriptionId,
      cancelAtPeriodEnd: true
    })
  });

  const data = await response.json();
  return data;
}

// Update subscription
async function updateSubscription(subscriptionId: string, newPriceId: string) {
  const response = await fetch(`/api/payments/subscriptions/${subscriptionId}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      priceId: newPriceId,
      prorationBehavior: 'create_prorations'
    })
  });

  const data = await response.json();
  return data;
}
```

### Usage Tracking

```typescript
// Record API usage
async function recordApiUsage(quantity: number) {
  const response = await fetch('/api/payments/usage', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      quantity,
      action: 'increment'
    })
  });

  const data = await response.json();
  return data;
}
```
