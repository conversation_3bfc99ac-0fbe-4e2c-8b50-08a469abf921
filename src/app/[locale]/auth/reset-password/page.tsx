import { Suspense } from 'react';
import { getTranslations } from 'next-intl/server';
import { setRequestLocale } from 'next-intl/server';
import ResetPasswordForm from './ResetPasswordForm';

// 服务器组件
export default async function ResetPassword(
  props: {
    params: Promise<{ locale: string }>;
    searchParams: Promise<{ token?: string }>;
  }
) {
  const searchParams = await props.searchParams;
  // 使用 await 获取 locale
  const { locale } = await props.params;

  // 设置请求的 locale
  setRequestLocale(locale);

  // 获取翻译
  const t = await getTranslations('auth');

  // 获取token参数
  const token = searchParams?.token || '';

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
            {t('resetPasswordTitle')}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {t('resetPasswordDescription')}
          </p>
        </div>
        <Suspense fallback={<div className="text-center">Loading...</div>}>
          <ResetPasswordForm token={token} locale={locale} />
        </Suspense>
      </div>
    </div>
  );
}
