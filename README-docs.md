# Fumadocs Documentation System

This document provides comprehensive instructions for the Fumadocs documentation system integrated into the ShipSaaS project.

## Overview

Fumadocs is a modern documentation framework built for Next.js applications. It provides:

- **MDX Support** - Write documentation in Markdown with React components
- **Search Functionality** - Built-in search with fuzzy matching
- **Beautiful UI** - Modern, responsive design with dark/light mode
- **Type Safety** - Full TypeScript support
- **Performance** - Optimized for speed and SEO

## Project Structure

```
├── content/
│   ├── docs/                    # Main documentation content
│   │   ├── meta.json            # Navigation structure
│   │   ├── index.mdx            # Homepage
│   │   ├── getting-started/     # Getting started section
│   │   ├── features/            # Features documentation
│   │   ├── api/                 # API reference
│   │   ├── deployment/          # Deployment guides
│   │   └── advanced/            # Advanced topics
│   ├── blog/                    # Blog posts (optional)
│   └── changelog/               # Changelog entries (optional)
├── src/
│   ├── app/
│   │   ├── docs/                # Documentation routes
│   │   │   ├── layout.tsx       # Docs layout
│   │   │   └── [[...slug]]/     # Dynamic docs pages
│   │   ├── api/search/          # Search API
│   │   └── layout.config.tsx    # Layout configuration
│   └── lib/
│       └── source.ts            # Content source configuration
├── source.config.ts             # Fumadocs configuration
├── mdx-components.tsx           # MDX components
└── README-docs.md               # This file
```

## Installation & Setup

### 1. Dependencies

The following packages are already installed:

```json
{
  "dependencies": {
    "fumadocs-ui": "^15.6.3",
    "fumadocs-core": "^15.6.3",
    "fumadocs-mdx": "^11.6.10",
    "@types/mdx": "^2.0.13"
  }
}
```

### 2. Configuration Files

#### source.config.ts
Defines content collections and MDX processing options:

```typescript
import { defineDocs, defineCollections } from 'fumadocs-mdx/config';

export const { docs, meta } = defineDocs({
  dir: 'content/docs',
});

export const blog = defineCollections({
  type: 'doc',
  dir: 'content/blog',
  schema: frontmatterSchema.extend({
    author: z.string().optional(),
    date: z.string().date().or(z.date()).optional(),
  }),
});
```

#### next.config.mjs
Integrates Fumadocs MDX processing:

```javascript
import { createMDX } from 'fumadocs-mdx/next';

const withMDX = createMDX();

export default withBundleAnalyzer(withNextIntl(withMDX(nextConfig)));
```

#### mdx-components.tsx
Configures MDX components:

```typescript
import defaultMdxComponents from 'fumadocs-ui/mdx';
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    Tab,
    Tabs,
    ...components,
  };
}
```

### 3. Styling

Fumadocs styles are imported in `src/app/globals.css`:

```css
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';
```

## Creating Documentation

### 1. Writing MDX Files

Create `.mdx` files in the `content/docs/` directory:

```mdx
---
title: Page Title
description: Page description for SEO
---

# Page Title

Your content here with **markdown** formatting.

## Code Examples

```javascript
const example = "Hello World";
```

## Components

<Callout type="info">
  This is an info callout.
</Callout>

<Tabs items={['npm', 'pnpm', 'yarn']}>
  <Tab value="npm">
    ```bash
    npm install package
    ```
  </Tab>
  <Tab value="pnpm">
    ```bash
    pnpm add package
    ```
  </Tab>
</Tabs>
```

### 2. Navigation Structure

Define navigation in `meta.json` files:

```json
{
  "title": "Section Title",
  "pages": [
    "page-slug",
    {
      "title": "Subsection",
      "pages": [
        "subsection/page1",
        "subsection/page2"
      ]
    }
  ]
}
```

### 3. Frontmatter Options

Available frontmatter fields:

```yaml
---
title: "Page Title"                    # Required
description: "Page description"        # Recommended for SEO
full: false                           # Full-width layout
toc: true                             # Table of contents
---
```

## Available Components

### Built-in Components

- **Callout** - Info, warning, error callouts
- **Tabs** - Tabbed content sections
- **CodeBlock** - Enhanced code blocks with copy button
- **Card** - Content cards
- **Steps** - Step-by-step guides

### Usage Examples

```mdx
<Callout type="warning">
  Important warning message
</Callout>

<Tabs items={['Option 1', 'Option 2']}>
  <Tab value="Option 1">Content 1</Tab>
  <Tab value="Option 2">Content 2</Tab>
</Tabs>
```

## Search Functionality

Search is automatically enabled with the API route at `/api/search`. Features:

- **Fuzzy Search** - Finds content even with typos
- **Instant Results** - Real-time search as you type
- **Keyboard Navigation** - Use arrow keys and Enter
- **Highlighting** - Search terms are highlighted

## Customization

### 1. Layout Configuration

Modify `src/app/layout.config.tsx`:

```typescript
export const baseOptions: BaseLayoutProps = {
  nav: {
    title: 'Your Docs',
    transparentMode: 'top',
  },
  links: [
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
  ],
  githubUrl: 'https://github.com/your-repo',
};
```

### 2. Custom Components

Add custom components to `mdx-components.tsx`:

```typescript
export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    // Custom components
    CustomComponent: ({ children }) => (
      <div className="custom-style">{children}</div>
    ),
    ...components,
  };
}
```

### 3. Styling

Customize appearance by:

- Modifying Tailwind CSS classes
- Adding custom CSS in `globals.css`
- Using CSS variables for theming

## Development Workflow

### 1. Adding New Pages

1. Create `.mdx` file in appropriate directory
2. Add to `meta.json` navigation
3. Run `pnpm dev` to see changes

### 2. Content Organization

- Use descriptive file names
- Group related content in folders
- Maintain consistent frontmatter
- Update navigation structure

### 3. Testing

```bash
# Development server
pnpm dev

# Build and test
pnpm build
pnpm start

# Generate documentation
pnpm postinstall
```

## Deployment

### 1. Build Process

The documentation is built automatically with:

```bash
pnpm build
```

This runs:
1. `fumadocs-mdx` - Generates content
2. `next build` - Builds the application

### 2. Static Generation

All documentation pages are statically generated for optimal performance.

### 3. Environment Variables

No additional environment variables required for basic documentation.

## Troubleshooting

### Common Issues

**Build Errors**
- Ensure all `.mdx` files have valid frontmatter
- Check `meta.json` syntax
- Verify file paths match navigation structure

**Search Not Working**
- Check `/api/search` endpoint
- Verify content is being indexed
- Clear browser cache

**Styling Issues**
- Ensure Fumadocs CSS is imported
- Check for conflicting styles
- Verify Tailwind configuration

**Content Not Updating**
- Run `pnpm postinstall` to regenerate
- Clear `.next` cache
- Restart development server

### Getting Help

1. Check the [Fumadocs Documentation](https://fumadocs.vercel.app/)
2. Review error messages in console
3. Verify file structure and naming
4. Test with minimal example

## Best Practices

### Content Writing

- Use clear, descriptive titles
- Include descriptions for SEO
- Structure content with headings
- Add code examples where relevant
- Use callouts for important information

### File Organization

- Group related content in folders
- Use consistent naming conventions
- Maintain logical navigation structure
- Keep file names URL-friendly

### Performance

- Optimize images before adding
- Use appropriate heading levels
- Minimize large code blocks
- Test on different devices

## Advanced Features

### 1. Custom Search

Implement custom search logic in `/api/search/route.ts`.

### 2. Multiple Collections

Add blog or changelog collections in `source.config.ts`.

### 3. Internationalization

Extend for multiple languages using Next.js i18n.

### 4. Custom Layouts

Create specialized layouts for different content types.

## Maintenance

### Regular Tasks

- Update dependencies monthly
- Review and update content
- Check for broken links
- Monitor search performance
- Backup content regularly

### Content Updates

- Follow semantic versioning for major changes
- Use git for version control
- Test changes before deployment
- Maintain changelog for significant updates

---

For more information, visit the [Fumadocs Documentation](https://fumadocs.vercel.app/) or check the project's GitHub repository.
