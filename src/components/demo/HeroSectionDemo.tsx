"use client";

import { HeroSection } from "@/components/blocks/hero-section-dark";

/**
 * HeroSectionDemo - Showcases the optimized Hero section with enhanced styling
 * 英雄区演示组件 - 展示优化后的英雄区，具有增强的样式设计
 * 
 * @description This demo component showcases the enhanced HeroSection with:
 * - Improved visual hierarchy and typography
 * - Enhanced background gradients and effects
 * - Better responsive design
 * - Smooth animations and hover effects
 * - Modern glassmorphism design elements
 * 
 * @features
 * - Multi-layered background with gradients
 * - Enhanced typography with better font scaling
 * - Animated CTA button with spinning border
 * - Improved image presentation with glow effects
 * - Better spacing and layout optimization
 */
export function HeroSectionDemo() {
  return (
    <HeroSection
      title="Welcome to Our Platform"
      subtitle={{
        regular: "Transform your ideas into ",
        gradient: "beautiful digital experiences",
      }}
      description="Transform your ideas into reality with our comprehensive suite of development tools and resources."
      ctaText="Get Started"
      ctaHref="/signup"
      bottomImage={{
        light: "https://www.launchuicomponents.com/app-light.png",
        dark: "https://www.launchuicomponents.com/app-dark.png",
      }}
      gridOptions={{
        angle: 65,
        opacity: 0.4,
        cellSize: 50,
        lightLineColor: "rgba(148, 163, 184, 0.25)",
        darkLineColor: "rgba(71, 85, 105, 0.35)",
      }}
      className="overflow-hidden"
    />
  );
}
