---
title: API Reference
description: Complete API reference for ShipSaaS endpoints and integrations
---

# API Reference

Complete API reference for ShipSaaS endpoints and integrations

ShipSaaS provides a comprehensive REST API for managing users, subscriptions, and integrations. This reference covers all available endpoints with examples.

## Base URL

```
https://yourdomain.com/api
```

## Authentication

Most API endpoints require authentication using session cookies or API keys.

### Session Authentication

For web applications, use session-based authentication:

```typescript
// Client-side authentication check
import { useSession } from 'next-auth/react';

function Component() {
  const { data: session, status } = useSession();
  
  if (status === 'loading') return <p>Loading...</p>;
  if (status === 'unauthenticated') return <p>Access Denied</p>;
  
  return <p>Welcome {session.user.email}</p>;
}
```

### API Key Authentication

For server-to-server communication, use API keys:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://yourdomain.com/api/users
```

## Core Endpoints

### Authentication API

Manage user authentication and sessions.

**Endpoints:**
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `POST /api/auth/reset-password` - Password reset
- `GET /api/auth/session` - Get current session

For detailed authentication API, see the [Authentication API](/docs/api-reference/authentication) reference.

### User Management API

Manage user accounts and profiles.

**Endpoints:**
- `GET /api/users` - List users
- `GET /api/users/[id]` - Get user details
- `PUT /api/users/[id]` - Update user
- `DELETE /api/users/[id]` - Delete user
- `GET /api/users/me` - Get current user

For detailed user API, see the [Users API](/docs/api-reference/users) reference.

### Payment API

Handle subscriptions and payment processing.

**Endpoints:**
- `POST /api/payments/create-checkout` - Create checkout session
- `POST /api/payments/create-portal` - Create customer portal
- `GET /api/payments/subscriptions` - List subscriptions
- `POST /api/payments/cancel` - Cancel subscription
- `POST /api/stripe/webhook` - Stripe webhook handler

For detailed payment API, see the [Payments API](/docs/api-reference/payments) reference.

### GitHub Integration API

Manage GitHub repository access and invitations.

**Endpoints:**
- `POST /api/github/invite` - Send repository invitation
- `GET /api/github/repositories` - List accessible repositories
- `DELETE /api/github/revoke` - Revoke repository access
- `GET /api/github/status` - Check invitation status

For detailed GitHub API, see the [GitHub API](/docs/api-reference/github) reference.

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details
    }
  }
}
```

## Status Codes

ShipSaaS API uses standard HTTP status codes:

- `200` - OK: Request successful
- `201` - Created: Resource created successfully
- `400` - Bad Request: Invalid request parameters
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute
- **User endpoints**: 100 requests per minute
- **Payment endpoints**: 20 requests per minute
- **GitHub endpoints**: 50 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination using query parameters:

```bash
GET /api/users?page=1&limit=20&sort=created_at&order=desc
```

**Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sort` - Sort field (default: created_at)
- `order` - Sort order: asc or desc (default: desc)

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Filtering

Many endpoints support filtering using query parameters:

```bash
GET /api/users?status=active&role=user&created_after=2024-01-01
```

## Webhooks

ShipSaaS supports webhooks for real-time event notifications.

### Webhook Events

- `user.created` - New user registered
- `user.updated` - User profile updated
- `subscription.created` - New subscription
- `subscription.updated` - Subscription changed
- `subscription.cancelled` - Subscription cancelled
- `payment.succeeded` - Payment successful
- `payment.failed` - Payment failed

For webhook configuration, see the [Webhooks API](/docs/api-reference/webhooks) reference.

## SDK and Libraries

### JavaScript/TypeScript SDK

```typescript
import { ShipSaaSClient } from '@shipsaas/sdk';

const client = new ShipSaaSClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://yourdomain.com/api'
});

// Example usage
const users = await client.users.list();
```

### Python SDK

```python
from shipsaas import ShipSaaSClient

client = ShipSaaSClient(
    api_key='your-api-key',
    base_url='https://yourdomain.com/api'
)

# Example usage
users = client.users.list()
```

## Error Handling

Handle API errors gracefully in your applications:

```typescript
try {
  const response = await fetch('/api/users', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData)
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error.message);
  }
  
  const data = await response.json();
  return data;
} catch (error) {
  console.error('API Error:', error.message);
  // Handle error appropriately
}
```

## Testing

Test API endpoints using the provided test utilities:

```typescript
import { testApiHandler } from 'next-test-api-route-handler';
import handler from '../pages/api/users';

test('GET /api/users returns user list', async () => {
  await testApiHandler({
    handler,
    test: async ({ fetch }) => {
      const res = await fetch({ method: 'GET' });
      expect(res.status).toBe(200);
    }
  });
});
```

## Next Steps

Explore specific API references:

- [Authentication API](/docs/api-reference/authentication)
- [Users API](/docs/api-reference/users)
- [Payments API](/docs/api-reference/payments)
- [GitHub API](/docs/api-reference/github)
- [Webhooks API](/docs/api-reference/webhooks)
