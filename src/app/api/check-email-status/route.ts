import { NextResponse } from 'next/server';
import { checkEmailStatus, validateEmailConfig } from '@/lib/email-status';

/**
 * 检查邮件发送状态的API端点
 * 仅用于开发环境测试
 */
export async function GET(request: Request) {
  // 只在开发环境允许访问
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  const { searchParams } = new URL(request.url);
  const emailId = searchParams.get('id');
  const action = searchParams.get('action') || 'status';

  try {
    if (action === 'config') {
      // 检查邮件配置
      const configCheck = validateEmailConfig();
      
      console.log('🔧 Email configuration check:');
      console.log(`   ✅ Valid: ${configCheck.isValid}`);
      console.log(`   📧 API Key: ${configCheck.config.apiKey ? 'Set' : 'Not set'}`);
      console.log(`   📧 From Email: ${configCheck.config.fromEmail ? 'Set' : 'Not set'}`);
      
      if (configCheck.issues.length > 0) {
        console.log('   ⚠️ Issues:');
        configCheck.issues.forEach(issue => console.log(`     - ${issue}`));
      }

      return NextResponse.json({
        valid: configCheck.isValid,
        issues: configCheck.issues,
        config: configCheck.config,
        environment: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
      });
    }

    if (action === 'status') {
      if (!emailId) {
        return NextResponse.json(
          { error: 'Email ID is required for status check. Use ?id=email_id' },
          { status: 400 }
        );
      }

      const emailStatus = await checkEmailStatus(emailId);
      
      return NextResponse.json({
        success: true,
        email: emailStatus,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Use ?action=config or ?action=status&id=email_id' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ Error checking email status:');
    console.error('   Error:', error);
    console.error('   Email ID:', emailId);
    console.error('   Action:', action);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      emailId,
      action,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
