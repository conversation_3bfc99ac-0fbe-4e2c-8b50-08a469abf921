"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle, Github, Loader2 } from "lucide-react";

/**
 * GitHub Permission Check Demo Component
 * GitHub 权限检查演示组件
 */
export function GitHubPermissionCheck() {
  const [checking, setChecking] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleCheckPermissions = async () => {
    setChecking(true);
    setResult(null);

    try {
      const response = await fetch("/api/github/check-permissions");
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error("Error checking permissions:", error);
      setResult({ error: "Failed to check permissions" });
    } finally {
      setChecking(false);
    }
  };

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === true) return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (status === false) return <XCircle className="w-5 h-5 text-red-500" />;
    return <AlertCircle className="w-5 h-5 text-yellow-500" />;
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">GitHub Permission Check</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Check GitHub API token permissions and repository access for debugging
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Permission Status</h2>
            <Button
              onClick={handleCheckPermissions}
              disabled={checking}
              className="bg-gradient-to-r from-gray-900 to-gray-700 hover:from-gray-800 hover:to-gray-600"
            >
              {checking ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                <>
                  <Github className="w-4 h-4 mr-2" />
                  Check Permissions
                </>
              )}
            </Button>
          </div>

          {result && (
            <div className="space-y-6">
              {/* Token Configuration */}
              <div className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                {getStatusIcon(result.tokenConfigured)}
                <div>
                  <p className="font-medium">GitHub Token Configuration</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {result.tokenConfigured ? "Token is configured" : "Token not found in environment"}
                  </p>
                </div>
              </div>

              {/* User Authentication */}
              {result.user && (
                <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <div className="flex-1">
                    <p className="font-medium">GitHub User Authentication</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Authenticated as: <strong>{result.user.login}</strong> ({result.user.name || 'No name'})
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      User ID: {result.user.id} | Type: {result.user.type}
                    </p>
                  </div>
                </div>
              )}

              {/* Token Scopes */}
              {result.scopes && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-blue-500" />
                    <p className="font-medium">Token Scopes</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {result.scopes.map((scope: string) => (
                      <span
                        key={scope}
                        className="px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                      >
                        {scope}
                      </span>
                    ))}
                  </div>
                  <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                    Required scopes: <strong>repo</strong>, <strong>admin:org</strong> (for organization repositories)
                  </div>
                </div>
              )}

              {/* Repository Access */}
              {result.repositoryAccess && (
                <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <div className="flex-1">
                    <p className="font-medium">Repository Access</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Can access: <strong>{result.repositoryAccess.full_name}</strong>
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Private: {result.repositoryAccess.private ? 'Yes' : 'No'} | 
                      Permissions: {JSON.stringify(result.repositoryAccess.permissions || {})}
                    </p>
                  </div>
                </div>
              )}

              {/* Rate Limit */}
              {result.rateLimit && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-5 h-5 text-yellow-500" />
                    <p className="font-medium">Rate Limit Status</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Core API</p>
                      <p className="font-medium">
                        {result.rateLimit.resources.core.remaining} / {result.rateLimit.resources.core.limit}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">Search API</p>
                      <p className="font-medium">
                        {result.rateLimit.resources.search.remaining} / {result.rateLimit.resources.search.limit}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Error Message */}
              {result.error && (
                <div className="flex items-center gap-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <XCircle className="w-5 h-5 text-red-500" />
                  <div>
                    <p className="font-medium text-red-800 dark:text-red-200">Error</p>
                    <p className="text-sm text-red-600 dark:text-red-300">{result.error}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h3 className="font-semibold mb-2 text-yellow-800 dark:text-yellow-200">
              Setup Instructions
            </h3>
            <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <p>1. Create a GitHub Personal Access Token at: https://github.com/settings/tokens</p>
              <p>2. Grant the following permissions:</p>
              <ul className="ml-4 list-disc">
                <li><strong>repo</strong> - Full control of private repositories</li>
                <li><strong>admin:org</strong> - Full control of orgs and teams (for organization repositories)</li>
              </ul>
              <p>3. Add the token to your .env file: <code>GITHUB_API_TOKEN=your_token_here</code></p>
              <p>4. Ensure the token owner has admin access to the target repository</p>
              <p>5. Restart your development server after adding the token</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
