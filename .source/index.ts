// @ts-nocheck -- skip type checking
import * as meta_2 from "../content/docs/getting-started/meta.json?collection=meta&hash=1752163574841"
import * as meta_1 from "../content/docs/features/meta.json?collection=meta&hash=1752163574841"
import * as meta_0 from "../content/docs/meta.json?collection=meta&hash=1752163574841"
import * as docs_8 from "../content/docs/getting-started/quick-start.mdx?collection=docs&hash=1752163574841"
import * as docs_7 from "../content/docs/getting-started/introduction.mdx?collection=docs&hash=1752163574841"
import * as docs_6 from "../content/docs/getting-started/installation.mdx?collection=docs&hash=1752163574841"
import * as docs_5 from "../content/docs/getting-started/environment-setup.mdx?collection=docs&hash=1752163574841"
import * as docs_4 from "../content/docs/features/payments.mdx?collection=docs&hash=1752163574841"
import * as docs_3 from "../content/docs/features/github-integration.mdx?collection=docs&hash=1752163574841"
import * as docs_2 from "../content/docs/features/authentication.mdx?collection=docs&hash=1752163574841"
import * as docs_1 from "../content/docs/test.mdx?collection=docs&hash=1752163574841"
import * as docs_0 from "../content/docs/index.mdx?collection=docs&hash=1752163574841"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"index.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/index.mdx"}, data: docs_0 }, { info: {"path":"test.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/test.mdx"}, data: docs_1 }, { info: {"path":"features/authentication.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/features/authentication.mdx"}, data: docs_2 }, { info: {"path":"features/github-integration.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/features/github-integration.mdx"}, data: docs_3 }, { info: {"path":"features/payments.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/features/payments.mdx"}, data: docs_4 }, { info: {"path":"getting-started/environment-setup.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/getting-started/environment-setup.mdx"}, data: docs_5 }, { info: {"path":"getting-started/installation.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/getting-started/installation.mdx"}, data: docs_6 }, { info: {"path":"getting-started/introduction.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/getting-started/introduction.mdx"}, data: docs_7 }, { info: {"path":"getting-started/quick-start.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/getting-started/quick-start.mdx"}, data: docs_8 }]);
export const meta = _runtime.meta<typeof _source.meta>([{ info: {"path":"meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/meta.json"}, data: meta_0 }, { info: {"path":"features/meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/features/meta.json"}, data: meta_1 }, { info: {"path":"getting-started/meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/shipsaas-office/content/docs/getting-started/meta.json"}, data: meta_2 }]);