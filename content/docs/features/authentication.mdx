---
title: Authentication System
description: Complete user authentication with NextAuth.js, email verification, and OAuth providers
---

# Authentication System

ShipSaaS includes a comprehensive authentication system built with NextAuth.js, providing secure user management with multiple authentication methods.

## Features

### Core Authentication
- **Email/Password** - Traditional email and password authentication
- **OAuth Providers** - Google, GitHub, and other OAuth providers
- **Email Verification** - Secure email verification process
- **Password Reset** - Secure password reset with time-limited tokens
- **Session Management** - Secure session handling with JWT

### Security Features
- **CSRF Protection** - Built-in CSRF protection
- **Rate Limiting** - Prevent brute force attacks
- **Secure Cookies** - HTTP-only, secure cookies
- **Password Hashing** - bcrypt password hashing
- **Token Expiration** - Configurable token expiration

## Configuration

### NextAuth.js Setup

The authentication is configured in `src/auth.config.ts`:

```typescript
import { NextAuthConfig } from 'next-auth';
import Google from 'next-auth/providers/google';
import Credentials from 'next-auth/providers/credentials';

export const authConfig: NextAuthConfig = {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Credentials({
      async authorize(credentials) {
        // Custom credential validation
      },
    }),
  ],
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      // Customize session data
    },
  },
};
```

### Environment Variables

Required environment variables:

```bash
# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

## Usage

### Sign Up Process

1. **User Registration**
   ```typescript
   const response = await fetch('/api/auth/signup', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       email: '<EMAIL>',
       password: 'securepassword',
       name: 'John Doe'
     })
   });
   ```

2. **Email Verification**
   - User receives verification email
   - Clicks verification link
   - Account is activated

### Sign In Methods

#### Email/Password
```typescript
import { signIn } from 'next-auth/react';

await signIn('credentials', {
  email: '<EMAIL>',
  password: 'password',
  redirect: false
});
```

#### OAuth Providers
```typescript
import { signIn } from 'next-auth/react';

// Google OAuth
await signIn('google');

// GitHub OAuth
await signIn('github');
```

### Password Reset

1. **Request Reset**
   ```typescript
   const response = await fetch('/api/auth/reset-password', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ email: '<EMAIL>' })
   });
   ```

2. **Reset Password**
   ```typescript
   const response = await fetch('/api/auth/reset-password/confirm', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       token: 'reset-token',
       password: 'newpassword'
     })
   });
   ```

### Session Management

#### Client-Side
```typescript
import { useSession } from 'next-auth/react';

function Profile() {
  const { data: session, status } = useSession();
  
  if (status === 'loading') return <p>Loading...</p>;
  if (status === 'unauthenticated') return <p>Not signed in</p>;
  
  return <p>Signed in as {session.user.email}</p>;
}
```

#### Server-Side
```typescript
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth.config';

export async function GET() {
  const session = await getServerSession(authConfig);
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  return Response.json({ user: session.user });
}
```

## Customization

### Custom Sign-In Page

Create custom authentication pages in `src/app/[locale]/auth/`:

```typescript
// src/app/[locale]/auth/signin/page.tsx
import { SignInForm } from '@/components/auth/signin-form';

export default function SignInPage() {
  return (
    <div className="container mx-auto max-w-md">
      <h1>Sign In</h1>
      <SignInForm />
    </div>
  );
}
```

### Custom Callbacks

Customize authentication behavior:

```typescript
export const authConfig: NextAuthConfig = {
  callbacks: {
    async signIn({ user, account, profile }) {
      // Custom sign-in logic
      return true;
    },
    async session({ session, token }) {
      // Add custom data to session
      session.user.id = token.sub;
      return session;
    },
    async jwt({ token, user }) {
      // Customize JWT token
      if (user) {
        token.role = user.role;
      }
      return token;
    },
  },
};
```

### Role-Based Access

Implement role-based access control:

```typescript
// Middleware for protected routes
export function withAuth(handler: NextApiHandler, roles?: string[]) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const session = await getServerSession(req, res, authConfig);
    
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    if (roles && !roles.includes(session.user.role)) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    
    return handler(req, res);
  };
}
```

## Security Best Practices

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Rate Limiting
```typescript
// Implement rate limiting for auth endpoints
import rateLimit from 'express-rate-limit';

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts'
});
```

### Session Security
- Secure cookie settings
- HTTPS in production
- Regular session rotation
- Proper logout handling

## Advanced Features

### Multi-Factor Authentication (MFA)

Enable additional security with MFA support:

```typescript
// Enable MFA for user
const enableMFA = async (userId: string) => {
  const secret = speakeasy.generateSecret({
    name: 'ShipSaaS',
    account: user.email,
  });

  await prisma.user.update({
    where: { id: userId },
    data: { mfaSecret: secret.base32 }
  });

  return secret.otpauth_url;
};
```

### Account Linking

Link multiple OAuth accounts to a single user:

```typescript
// Link OAuth account
export const authConfig: NextAuthConfig = {
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider !== 'credentials') {
        // Link OAuth account to existing user
        const existingUser = await getUserByEmail(user.email);
        if (existingUser) {
          await linkAccount(existingUser.id, account);
        }
      }
      return true;
    }
  }
};
```

### Custom Authentication Flow

Implement custom authentication logic:

```typescript
// Custom authentication provider
const CustomProvider = Credentials({
  name: 'custom',
  credentials: {
    token: { label: 'Token', type: 'text' }
  },
  async authorize(credentials) {
    // Custom validation logic
    const user = await validateCustomToken(credentials.token);
    return user ? { id: user.id, email: user.email } : null;
  }
});
```

## Security Best Practices

### Password Security

Implement strong password requirements:

```typescript
// Password validation schema
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain uppercase letter')
  .regex(/[a-z]/, 'Password must contain lowercase letter')
  .regex(/[0-9]/, 'Password must contain number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain special character');
```

### Session Security

Configure secure session settings:

```typescript
export const authConfig: NextAuthConfig = {
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  }
};
```

### Rate Limiting

Implement rate limiting for authentication endpoints:

```typescript
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(5, '1 m'), // 5 requests per minute
});

export async function POST(request: Request) {
  const ip = request.headers.get('x-forwarded-for') ?? 'anonymous';
  const { success } = await ratelimit.limit(ip);

  if (!success) {
    return new Response('Too many requests', { status: 429 });
  }

  // Continue with authentication logic
}
```

## Troubleshooting

### Common Issues

**OAuth Provider Setup**
```bash
# Verify OAuth configuration
echo "Google Client ID: $GOOGLE_CLIENT_ID"
echo "Redirect URI: $NEXTAUTH_URL/api/auth/callback/google"

# Test OAuth flow
curl -X GET "$NEXTAUTH_URL/api/auth/signin/google"
```

**Email Verification Issues**
```typescript
// Debug email sending
const sendVerificationEmail = async (email: string, token: string) => {
  try {
    const result = await resend.emails.send({
      from: process.env.RESEND_FROM_EMAIL,
      to: email,
      subject: 'Verify your email',
      html: `<a href="${process.env.NEXTAUTH_URL}/verify?token=${token}">Verify</a>`
    });
    console.log('Email sent:', result);
  } catch (error) {
    console.error('Email error:', error);
  }
};
```

**Session Debugging**
```typescript
// Debug session issues
import { getServerSession } from 'next-auth';

export async function GET() {
  const session = await getServerSession(authConfig);

  return Response.json({
    session,
    timestamp: new Date().toISOString(),
    env: {
      NEXTAUTH_SECRET: !!process.env.NEXTAUTH_SECRET,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      DATABASE_URL: !!process.env.DATABASE_URL
    }
  });
}
```

**Database Connection Issues**
```bash
# Test database connection
pnpm prisma db pull

# Check user table
pnpm prisma studio
```

### Error Messages

**Common Error Codes:**
- `SIGNIN_EMAIL_ERROR` - Email sending failed
- `CALLBACK_URL_ERROR` - Invalid callback URL
- `OAUTH_CALLBACK_ERROR` - OAuth provider error
- `SESSION_REQUIRED` - Authentication required
- `CSRF_TOKEN_MISMATCH` - CSRF validation failed

**Debugging Steps:**
1. Check environment variables
2. Verify database connection
3. Test email service
4. Validate OAuth configuration
5. Check browser console for errors

### Performance Optimization

**Session Optimization:**
```typescript
// Optimize session callbacks
export const authConfig: NextAuthConfig = {
  callbacks: {
    async session({ session, token }) {
      // Only include necessary data
      return {
        ...session,
        user: {
          id: token.sub,
          email: session.user.email,
          role: token.role
        }
      };
    },
    async jwt({ token, user, trigger }) {
      // Minimize token size
      if (user) {
        token.role = user.role;
      }
      return token;
    }
  }
};
```

For more authentication details, see the [Authentication API Reference](/docs/api-reference/authentication).
