import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';
import { ExternalLink, MessageCircle } from 'lucide-react';

export const baseOptions: BaseLayoutProps = {
  nav: {
    title: 'ShipSaaS Docs',
    transparentMode: 'top',
  },
  links: [
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
    // {
    //   text: 'GitHub',
    //   url: 'https://github.com/wenhaofree/shipsaas-office',
    //   external: true,
    //   icon: <ExternalLink className="h-4 w-4" />,
    // },
  ],
  githubUrl: 'https://github.com/wenhaofree/shipsaas-office',
};
