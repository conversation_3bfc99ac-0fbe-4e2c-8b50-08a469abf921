---
title: Deployment
description: Deploy your ShipSaaS application to production
---

# Deployment

Deploy your ShipSaaS application to production

ShipSaaS is designed to be deployed on modern hosting platforms with minimal configuration. This guide covers deployment options and best practices.

## Deployment Platforms

### Vercel (Recommended)

Vercel provides the best experience for Next.js applications with zero-configuration deployments.

**Features:**
- Automatic deployments from Git
- Built-in CDN and edge functions
- Environment variable management
- Preview deployments for pull requests
- Serverless functions support

**Getting Started:**
1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy with a single click

For detailed instructions, see the [Vercel Deployment](/docs/deployment/vercel) guide.

### Cloudflare Pages

Cloudflare Pages offers excellent performance with global edge deployment.

**Features:**
- Global edge network
- Unlimited bandwidth
- Built-in analytics
- Custom domains and SSL
- Edge-side rendering support

**Requirements:**
- All non-static routes must export `export const runtime = 'edge';`

For detailed instructions, see the [Cloudflare Pages Deployment](/docs/deployment/cloudflare-pages) guide.

### Railway

Railway provides a simple deployment experience with built-in database hosting.

**Features:**
- One-click PostgreSQL database
- Automatic HTTPS
- Environment variable management
- Git-based deployments
- Built-in monitoring

For detailed instructions, see the [Railway Deployment](/docs/deployment/railway) guide.

### Docker

Deploy ShipSaaS using Docker containers for maximum flexibility.

**Features:**
- Consistent environments
- Easy scaling
- Platform independence
- Container orchestration support

For detailed instructions, see the [Docker Deployment](/docs/deployment/docker) guide.

## Pre-Deployment Checklist

### Environment Variables

Ensure all production environment variables are configured:

- [ ] `DATABASE_URL` - Production database connection
- [ ] `NEXTAUTH_SECRET` - Secure random string
- [ ] `NEXTAUTH_URL` - Production domain URL
- [ ] `STRIPE_SECRET_KEY` - Live Stripe secret key
- [ ] `STRIPE_WEBHOOK_SECRET` - Production webhook secret
- [ ] `RESEND_API_KEY` - Resend API key
- [ ] `GITHUB_TOKEN` - GitHub API token

### Database Setup

- [ ] Production database created
- [ ] Database migrations applied
- [ ] Database connection tested
- [ ] Backup strategy configured

### External Services

- [ ] Stripe account configured for live mode
- [ ] Webhook endpoints configured
- [ ] Domain verification for email sending
- [ ] GitHub repositories set up
- [ ] SSL certificates configured

### Security

- [ ] Environment variables secured
- [ ] HTTPS enabled
- [ ] CORS configured properly
- [ ] Rate limiting enabled
- [ ] Security headers configured

## Build Configuration

### Next.js Configuration

Ensure your `next.config.js` is optimized for production:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  images: {
    domains: ['your-domain.com'],
  },
  // Add other production optimizations
};

module.exports = nextConfig;
```

### Build Scripts

Standard build commands for deployment:

```bash
# Install dependencies
pnpm install

# Generate Prisma client
pnpm db:generate

# Build the application
pnpm build

# Start production server
pnpm start
```

## Performance Optimization

### Caching Strategy

- Static assets cached at CDN level
- API responses cached appropriately
- Database queries optimized
- Image optimization enabled

### Monitoring

Set up monitoring for your production deployment:

- Application performance monitoring
- Error tracking and logging
- Database performance monitoring
- Uptime monitoring

## Troubleshooting

### Common Deployment Issues

**Build Failures**
- Check Node.js version compatibility
- Verify all dependencies are installed
- Ensure environment variables are set

**Database Connection Issues**
- Verify database URL format
- Check network connectivity
- Ensure database is accessible from deployment platform

**Environment Variable Issues**
- Verify all required variables are set
- Check for typos in variable names
- Ensure sensitive values are properly escaped

## Next Steps

Choose your preferred deployment platform:

- [Vercel Deployment](/docs/deployment/vercel)
- [Cloudflare Pages Deployment](/docs/deployment/cloudflare-pages)
- [Railway Deployment](/docs/deployment/railway)
- [Docker Deployment](/docs/deployment/docker)
