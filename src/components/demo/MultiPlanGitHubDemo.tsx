"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, CreditCard, Calendar, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { GitHubInviteModal } from "@/components/ui/github-invite-modal";

/**
 * Multi-Plan GitHub Demo Component
 * 多计划 GitHub 演示组件
 */
export function MultiPlanGitHubDemo() {
  const [githubModalOpen, setGithubModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  // Mock orders for different plans / 不同计划的模拟订单
  const mockOrders = [
    {
      id: 1,
      orderNo: "ORD-2024-001",
      amount: 9900, // $99.00
      status: "paid",
      createdAt: "2024-01-15T10:30:00Z",
      productName: "Starter - monthly",
      currency: "USD",
      paidAt: "2024-01-15T10:35:00Z",
      expectedRepository: "shipsaas-starter"
    },
    {
      id: 2,
      orderNo: "ORD-2024-002",
      amount: 19900, // $199.00
      status: "paid",
      createdAt: "2024-01-10T14:20:00Z",
      productName: "Pro - monthly",
      currency: "USD",
      paidAt: "2024-01-10T14:25:00Z",
      expectedRepository: "shipsaas-standard"
    },
    {
      id: 3,
      orderNo: "ORD-2024-003",
      amount: 29900, // $299.00
      status: "paid",
      createdAt: "2024-01-20T09:15:00Z",
      productName: "Enterprise - monthly",
      currency: "USD",
      paidAt: "2024-01-20T09:20:00Z",
      expectedRepository: "shipsaas-enterprise"
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-blue-500 hover:bg-blue-600";
      case "activated":
        return "bg-green-500 hover:bg-green-600";
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount / 100);
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      paid: "Paid",
      activated: "Activated",
      pending: "Pending",
      failed: "Failed",
      expired: "Expired",
    };
    return statusMap[status.toLowerCase()] || status;
  };

  const handleActivateOrder = (order: any) => {
    setSelectedOrder(order);
    setGithubModalOpen(true);
  };

  const handleCloseGithubModal = () => {
    setGithubModalOpen(false);
    setSelectedOrder(null);
  };

  const getPlanColor = (productName: string) => {
    const productLower = productName.toLowerCase();
    if (productLower.includes('starter')) {
      return 'from-blue-500 to-blue-600';
    } else if (productLower.includes('pro')) {
      return 'from-purple-500 to-purple-600';
    } else if (productLower.includes('enterprise')) {
      return 'from-orange-500 to-orange-600';
    }
    return 'from-gray-500 to-gray-600';
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Multi-Plan GitHub Repository Access</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Different pricing plans get access to different GitHub repositories
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
          {mockOrders.map((order) => (
            <div
              key={order.orderNo}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                    {order.productName}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Order ID: {order.orderNo}
                  </p>
                </div>
                <Badge className={`${getStatusColor(order.status)} text-white border-0`}>
                  {getStatusText(order.status)}
                </Badge>
              </div>

              <div className="grid grid-cols-1 gap-4 text-sm mb-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Amount</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatAmount(order.amount, order.currency)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Order Date</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {format(new Date(order.createdAt), "PPP")}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Paid Date</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {format(new Date(order.paidAt), "PPP")}
                    </p>
                  </div>
                </div>
              </div>

              {/* Repository Information */}
              <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Target Repository:</p>
                <p className="text-sm font-mono text-gray-800 dark:text-gray-200">
                  ShipSaaSCo/{order.expectedRepository}
                </p>
              </div>

              {/* Activation Button */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Payment confirmed</span>
                  </div>
                  <Button
                    onClick={() => handleActivateOrder(order)}
                    className={`bg-gradient-to-r ${getPlanColor(order.productName)} hover:opacity-90 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105`}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Activate Order
                  </Button>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Click to activate and get GitHub repository access
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Repository Mapping Information */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
            Repository Mapping
          </h3>
          <div className="grid gap-3 md:grid-cols-3">
            <div className="bg-white dark:bg-blue-800/30 p-3 rounded-lg">
              <h4 className="font-medium text-blue-800 dark:text-blue-200">Starter Plan</h4>
              <p className="text-sm text-blue-600 dark:text-blue-300 font-mono">ShipSaaSCo/shipsaas-starter</p>
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">Basic features and templates</p>
            </div>
            <div className="bg-white dark:bg-blue-800/30 p-3 rounded-lg">
              <h4 className="font-medium text-blue-800 dark:text-blue-200">Pro Plan</h4>
              <p className="text-sm text-blue-600 dark:text-blue-300 font-mono">ShipSaaSCo/shipsaas-standard</p>
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">Advanced features and integrations</p>
            </div>
            <div className="bg-white dark:bg-blue-800/30 p-3 rounded-lg">
              <h4 className="font-medium text-blue-800 dark:text-blue-200">Enterprise Plan</h4>
              <p className="text-sm text-blue-600 dark:text-blue-300 font-mono">ShipSaaSCo/shipsaas-enterprise</p>
              <p className="text-xs text-blue-500 dark:text-blue-400 mt-1">Enterprise features and support</p>
            </div>
          </div>
        </div>

        {/* GitHub Invite Modal */}
        {selectedOrder && (
          <GitHubInviteModal
            isOpen={githubModalOpen}
            onClose={handleCloseGithubModal}
            orderNo={selectedOrder.orderNo}
            productName={selectedOrder.productName}
          />
        )}
      </div>
    </div>
  );
}
