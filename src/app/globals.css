@tailwind base;
@tailwind components;
@tailwind utilities;

@import './theme.css';

/* Fumadocs UI Styles - Minimal custom overrides */
/* Fix sidebar left alignment */
[data-sidebar] {
  padding-left: 0 !important;
}

/* Fix sidebar navigation items - Left align */
.fd-sidebar nav a,
.fd-sidebar nav button {
  margin-left: 0 !important;
  text-align: left !important;
  justify-content: flex-start !important;
}

/* Improve search trigger sizing */
[data-search-trigger] {
  min-width: 280px !important;
  justify-content: flex-start !important;
  text-align: left !important;
  font-weight: normal !important;
}

/* Responsive search adjustments */
@media (max-width: 768px) {
  [data-search-trigger] {
    min-width: 200px !important;
  }
}

/* Base styles */
body {
  font-family: Arial, Helvetica, sans-serif;
}

html {
  scroll-behavior: smooth;
}

:root {
  --elegant-font: 'Great Vibes';
}

/* Component styles without @layer */
.tool-card {
  position: relative;
  padding: 1.25rem;
  border-radius: 0.75rem;
  border: 1px solid;
  transition: all 0.3s;
}

.tool-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-0.25rem);
}

/* Enhanced grid animation for hero section */
@keyframes grid {
  0% {
    transform: translateY(-50%);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-grid {
  animation: grid 15s linear infinite;
}

.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  border: 1px solid;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 600;
  transition: colors;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: colors;
}

.search-input {
  width: 100%;
  min-width: 300px;
  border-radius: 9999px;
  padding: 0.75rem 1.25rem;
  border: 1px solid;
  font-size: 1rem;
  outline: none;
}

.glass-card {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  border: 1px solid;
  transition: all;
}

.form-input {
  border-radius: 0.375rem;
  border: 1px solid;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  outline: none;
  transition: all;
}

.hover-scale {
  transition: transform 0.2s;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-lift {
  transition: all 0.3s;
}

.hover-lift:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}


