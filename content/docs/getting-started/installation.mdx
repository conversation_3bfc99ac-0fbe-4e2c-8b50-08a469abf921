---
title: Installation
description: Learn how to set up and run your ShipSaaS website in minutes
---

# Getting Started

Learn how to set up and run your ShipSaaS website in minutes

This guide will walk you through setting up ShipSaaS. We will go through the process of cloning the project, installing dependencies, setting up your database and running the local development server.

## Prerequisites

Before you begin, make sure you have the following installed:

### Node.js

Node.js is a runtime environment that allows you to run JavaScript code.

If you don't have Node.js installed, download it from the [Node.js official website](https://nodejs.org/).

```bash
# Check if Node.js is installed
node -v
```

### Git

Git is a version control system that is used to track changes in any set of files.

If Git is not installed, download it from the [Git official website](https://git-scm.com/).

```bash
# Check if Git is installed
git --version
```

### Package manager

Package manager is a tool that is used to manage dependencies in your project.

We recommend using [**pnpm**](https://pnpm.io/), but you can use [**npm**](https://www.npmjs.com/), [**yarn**](https://yarnpkg.com/) or [**bun**](https://bun.sh/) as well.

```bash
# Install pnpm if you haven't already
npm install -g pnpm

# Check pnpm version
pnpm --version
```

## Quick Installation

### Set up your project

There are many ways to download the source code of ShipSaaS. We'll cover some of the most common ones below. **Choose the one that best suits your needs.**

**Fork Repository (Recommended)**

We recommend forking the GitHub repository to get started.

When you visit the [GitHub repository](https://github.com/wenhaofree/shipsaas-office), you will see a "Fork" button in the top right corner. Clicking on it will create a copy of the ShipSaaS repository in your GitHub account, please keep this repository **private**.

Once you have forked the repository, you can clone your fork to your local machine:

```bash
git clone https://github.com/your-username/shipsaas-office.git your-project-name

cd your-project-name
```

**Clone Repository**

You can also clone the repository directly from GitHub.

```bash
git clone https://github.com/wenhaofree/shipsaas-office.git your-project-name

cd your-project-name

git remote remove origin
```

**About forking the repository**

- **Full History:** You maintain the full commit history of the project.
- **Easy Updates:** You can easily pull updates from the original repository using pull requests.
- **Contribution Ready:** If you want to contribute, your fork is already set up for that.
- **Only One Fork:** You can only have one fork of the repository, but you can clone the repository multiple times, so you can build multiple websites based on the template.

If you want to be able to pull updates from the ShipSaaS template repository, you can add the upstream remote, this will allow for an easier update process from the ShipSaaS repository.

```bash
git remote add upstream https://github.com/wenhaofree/shipsaas-office.git
```

### Install dependencies

Install the dependencies by running the following command:

```bash
pnpm install
```

This will install all required dependencies including:
- Next.js 15
- React 19
- Tailwind CSS
- Prisma ORM
- NextAuth.js
- Stripe
- Resend
- And many more...

## Step 3: Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/shipsaas"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# GitHub Integration
GITHUB_TOKEN="ghp_..."

# Email (Resend)
RESEND_API_KEY="re_..."
RESEND_FROM_EMAIL="<EMAIL>"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## Step 4: Database Setup

### Option A: Local PostgreSQL

1. Install PostgreSQL locally
2. Create a new database:
   ```sql
   CREATE DATABASE shipsaas;
   ```

### Option B: Cloud Database

Use a cloud provider like:
- [Supabase](https://supabase.com/) (recommended)
- [PlanetScale](https://planetscale.com/)
- [Railway](https://railway.app/)
- [Neon](https://neon.tech/)

### Initialize the Database

Run Prisma migrations:

```bash
pnpm db:push
```

Generate Prisma client:

```bash
pnpm db:generate
```

## Step 5: External Service Setup

### Stripe Configuration

1. Create a [Stripe account](https://stripe.com/)
2. Get your API keys from the Stripe dashboard
3. Set up webhooks pointing to `http://localhost:3000/api/stripe/webhook`
4. Configure your products and pricing in Stripe

### GitHub Integration (Optional)

1. Create a GitHub personal access token
2. Grant necessary permissions for repository management
3. Add the token to your environment variables

### Email Setup (Resend)

1. Sign up for [Resend](https://resend.com/)
2. Get your API key
3. Verify your sending domain

## Step 6: Start Development Server

```bash
pnpm dev
```

Your application will be available at `http://localhost:3000`.

## Verification

To verify everything is working correctly:

1. **Homepage** - Visit `http://localhost:3000`
2. **Authentication** - Try signing up/signing in
3. **Database** - Check if user data is saved
4. **Payments** - Test the payment flow (use Stripe test cards)

## Troubleshooting

### Common Issues

**Database Connection Error**
- Verify your `DATABASE_URL` is correct
- Ensure PostgreSQL is running
- Check firewall settings

**Stripe Webhook Issues**
- Use ngrok for local webhook testing
- Verify webhook endpoint URL
- Check webhook secret

**Build Errors**
- Clear `.next` folder: `rm -rf .next`
- Reinstall dependencies: `rm -rf node_modules && pnpm install`
- Check Node.js version compatibility

### Getting Help

If you encounter issues:

1. Check the [Troubleshooting Guide](/docs/advanced/troubleshooting)
2. Search existing GitHub issues
3. Create a new issue with detailed information

## Next Steps

Now that you have ShipSaaS running locally, check out the [Quick Start Guide](/docs/getting-started/quick-start) to learn how to customize it for your needs!
