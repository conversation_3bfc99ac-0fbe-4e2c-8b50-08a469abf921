import { NextResponse } from 'next/server';
import { Resend } from 'resend';

/**
 * 测试邮件发送功能的API端点
 * 仅用于开发环境测试
 */
export async function POST(request: Request) {
  // 只在开发环境允许访问
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'This endpoint is only available in development' },
      { status: 403 }
    );
  }

  try {
    const { to, subject = 'Test Email', message = 'This is a test email.' } = await request.json();

    if (!to) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json(
        { error: 'RESEND_API_KEY not configured' },
        { status: 500 }
      );
    }

    const resend = new Resend(process.env.RESEND_API_KEY);

    console.log('🧪 Testing email send...');
    console.log(`   📍 To: ${to}`);
    console.log(`   📍 From: ${process.env.NEXT_PUBLIC_FROM_EMAIL}`);
    console.log(`   📍 Subject: ${subject}`);
    console.log(`   📍 API Key: ${process.env.RESEND_API_KEY ? 'Set (***' + process.env.RESEND_API_KEY.slice(-4) + ')' : 'Not set'}`);

    const startTime = Date.now();
    const { data, error } = await resend.emails.send({
      from: process.env.NEXT_PUBLIC_FROM_EMAIL || '<EMAIL>',
      to: [to],
      subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #4F46E5;">Test Email</h2>
          <p>Hello!</p>
          <p>${message}</p>
          <p>This is a test email sent from your ShipSaaS application.</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
          <p style="color: #666; font-size: 14px;">
            Sent at: ${new Date().toISOString()}<br>
            Environment: ${process.env.NODE_ENV}
          </p>
        </div>
      `,
    });
    const duration = Date.now() - startTime;

    // 详细记录API响应
    console.log(`📊 Resend API Response (${duration}ms):`);
    console.log(`   📧 Data:`, data ? JSON.stringify(data, null, 2) : 'null');
    console.log(`   📧 Error:`, error ? JSON.stringify(error, null, 2) : 'null');

    if (error) {
      console.error('❌ Test email failed:');
      console.error('   📧 Error details:', JSON.stringify(error, null, 2));
      console.error('   📧 Error type:', typeof error);
      console.error('   📧 Error message:', error.message || 'No error message');
      console.error('   📧 Status code:', (error as any).statusCode || 'No status code');
      console.error('   📧 Error name:', error.name || 'No error name');

      return NextResponse.json({
        success: false,
        error: error.message || 'Failed to send email',
        details: {
          message: error.message,
          name: error.name,
          statusCode: (error as any).statusCode,
          fullError: error
        },
        duration,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (data && data.id) {
      console.log('✅ Test email sent successfully!');
      console.log(`   📧 Email ID: ${data.id}`);
      console.log(`   📧 To: ${to}`);
      console.log(`   📧 Subject: ${subject}`);
      console.log(`   📧 Duration: ${duration}ms`);
      console.log(`   📧 Timestamp: ${new Date().toISOString()}`);
      console.log(`   📧 Full response:`, JSON.stringify(data, null, 2));

      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        data: {
          id: data.id,
          to: [to],
          subject,
          duration,
          timestamp: new Date().toISOString(),
          fullResponse: data
        }
      });
    } else {
      console.warn('⚠️ Unexpected response from Resend API:');
      console.warn('   📧 Data:', data);
      console.warn('   📧 Expected data.id but got:', typeof data?.id);

      return NextResponse.json({
        success: false,
        error: 'Unexpected response format from email service',
        details: {
          responseData: data,
          expectedField: 'data.id',
          actualType: typeof data?.id
        },
        duration,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

  } catch (error) {
    console.error('💥 Critical error in test email send:');
    console.error('   Error:', error);
    console.error('   Stack:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('   Timestamp:', new Date().toISOString());

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
