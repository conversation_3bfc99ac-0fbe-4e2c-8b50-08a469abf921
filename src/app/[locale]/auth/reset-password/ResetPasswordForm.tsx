'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

interface ResetPasswordFormProps {
  token: string;
  locale: string;
}

export default function ResetPasswordForm({ token, locale }: ResetPasswordFormProps) {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const t = useTranslations('auth');
  const router = useRouter();

  // 检查token是否存在
  useEffect(() => {
    if (!token) {
      setError(t('invalidResetToken'));
    }
  }, [token, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setError(t('invalidResetToken'));
      return;
    }

    if (!password || !confirmPassword) {
      setError('All fields are required');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    if (password !== confirmPassword) {
      setError(t('passwordsDoNotMatch'));
      return;
    }

    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push(`/${locale}/auth/signin`);
        }, 3000);
      } else {
        let errorMessage = data.message || t('passwordResetError');
        
        // 根据错误类型显示相应的错误信息
        if (data.message?.includes('expired')) {
          errorMessage = t('tokenExpired');
        } else if (data.message?.includes('used')) {
          errorMessage = t('tokenAlreadyUsed');
        } else if (data.message?.includes('Invalid')) {
          errorMessage = t('invalidResetToken');
        }
        
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Reset password error:', err);
      setError(t('passwordResetError'));
    } finally {
      setLoading(false);
    }
  };

  const handleBackToSignIn = () => {
    router.push(`/${locale}/auth/signin`);
  };

  if (success) {
    return (
      <div className="mt-8 space-y-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            {t('passwordResetSuccess')}
          </h3>
          <p className="mt-2 text-sm text-gray-600">
            Redirecting to sign in page in 3 seconds...
          </p>
        </div>
        
        <div className="text-center">
          <button
            type="button"
            onClick={handleBackToSignIn}
            className="text-sm text-indigo-600 hover:text-indigo-500 font-medium"
          >
            {t('backToSignIn')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            {t('newPassword')}
          </label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={loading || !token}
            placeholder="Enter your new password"
          />
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
            {t('confirmNewPassword')}
          </label>
          <input
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            autoComplete="new-password"
            required
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={loading || !token}
            placeholder="Confirm your new password"
          />
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div>
          <button
            type="submit"
            disabled={loading || !token}
            className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? t('resettingPassword') : t('resetPassword')}
          </button>
        </div>

        <div className="text-center">
          <button
            type="button"
            onClick={handleBackToSignIn}
            className="text-sm text-indigo-600 hover:text-indigo-500 font-medium"
            disabled={loading}
          >
            {t('backToSignIn')}
          </button>
        </div>
      </form>
    </div>
  );
}
