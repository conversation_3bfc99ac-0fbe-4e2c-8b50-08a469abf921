import defaultMdxComponents from 'fumadocs-ui/mdx';
import { Tab, Tabs } from 'fumadocs-ui/components/tabs';
import { Callout } from 'fumadocs-ui/components/callout';
import { CodeBlock, Pre } from 'fumadocs-ui/components/codeblock';
import type { MDXComponents } from 'mdx/types';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    // Tab components for code examples
    Tab,
    Tabs,
    // Callout component for notes, warnings, etc.
    Callout,
    // Enhanced code blocks with copy functionality
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
    // Custom components can be added here
    ...components,
  };
}

// Export for Next.js MDX integration
export const useMDXComponents = getMDXComponents;
