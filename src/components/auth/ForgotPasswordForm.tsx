'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

interface ForgotPasswordFormProps {
  locale: string;
  onBackToSignIn: () => void;
}

export default function ForgotPasswordForm({ locale, onBackToSignIn }: ForgotPasswordFormProps) {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const t = useTranslations('auth');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError(t('email') + ' is required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setMessage('');

      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          locale,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        setMessage(t('resetLinkSent'));
      } else {
        setError(data.message || t('resetLinkError'));
      }
    } catch (err) {
      console.error('Forgot password error:', err);
      setError(t('resetLinkError'));
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="mt-8 space-y-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            {t('resetLinkSent')}
          </h3>
          <p className="mt-2 text-sm text-gray-600">
            {message}
          </p>
        </div>
        
        <div className="text-center">
          <button
            type="button"
            onClick={onBackToSignIn}
            className="text-sm text-indigo-600 hover:text-indigo-500 font-medium"
          >
            {t('backToSignIn')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">
          {t('forgotPasswordTitle')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('forgotPasswordDescription')}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            {t('email')}
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={loading}
            placeholder="<EMAIL>"
          />
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div>
          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? t('sendingResetLink') : t('sendResetLink')}
          </button>
        </div>

        <div className="text-center">
          <button
            type="button"
            onClick={onBackToSignIn}
            className="text-sm text-indigo-600 hover:text-indigo-500 font-medium"
            disabled={loading}
          >
            {t('backToSignIn')}
          </button>
        </div>
      </form>
    </div>
  );
}
