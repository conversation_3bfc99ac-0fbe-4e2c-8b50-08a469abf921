---
title: Configuration
description: Learn how to configure your ShipSaaS application for production
---

# Configuration

Learn how to configure your ShipSaaS application for production

ShipSaaS comes with sensible defaults, but you'll want to customize various aspects for your specific use case. This section covers all the configuration options available.

## Core Configuration

### Application Settings

Configure your application's basic settings in your environment variables:

```bash
# Application metadata
NEXT_PUBLIC_APP_NAME="Your SaaS Name"
NEXT_PUBLIC_APP_DESCRIPTION="Your SaaS Description"
NEXT_PUBLIC_APP_URL="https://yourdomain.com"

# Contact information
NEXT_PUBLIC_SUPPORT_EMAIL="<EMAIL>"
NEXT_PUBLIC_COMPANY_NAME="Your Company"
```

### Database Configuration

ShipSaaS uses Prisma ORM with PostgreSQL. Configure your database connection:

```bash
# PostgreSQL connection string
DATABASE_URL="postgresql://user:password@host:port/database"

# Optional: Direct database URL for migrations
DIRECT_URL="postgresql://user:password@host:port/database"
```

For detailed database setup, see the [Database Configuration](/docs/configuration/database) guide.

### Authentication Configuration

Configure authentication providers and settings:

```bash
# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="https://yourdomain.com"

# OAuth providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

For detailed authentication setup, see the [Authentication Configuration](/docs/configuration/authentication) guide.

## Service Integrations

### Payment Configuration

Configure Stripe for payment processing:

```bash
# Stripe keys
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_PUBLISHABLE_KEY="pk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Product pricing IDs
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID="price_..."
NEXT_PUBLIC_STRIPE_STANDARD_PRICE_ID="price_..."
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID="price_..."
```

For detailed payment setup, see the [Payment Configuration](/docs/configuration/payments) guide.

### Email Configuration

Configure Resend for transactional emails:

```bash
# Resend configuration
RESEND_API_KEY="re_..."
RESEND_FROM_EMAIL="<EMAIL>"
```

For detailed email setup, see the [Email Configuration](/docs/configuration/email) guide.

### GitHub Integration

Configure GitHub API for repository management:

```bash
# GitHub API token
GITHUB_TOKEN="ghp_..."

# Repository configuration
GITHUB_STARTER_REPO="your-org/starter-repo"
GITHUB_STANDARD_REPO="your-org/standard-repo"
GITHUB_PREMIUM_REPO="your-org/premium-repo"
```

For detailed GitHub setup, see the [GitHub Integration](/docs/configuration/github-integration) guide.

## Advanced Configuration

### Internationalization

ShipSaaS supports multiple languages out of the box. Configure supported locales:

```typescript
// src/i18n.config.ts
export const locales = ['en', 'zh'] as const;
export const defaultLocale = 'en' as const;
```

### Theme Configuration

Customize the application theme and styling:

```typescript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          // Your brand colors
        }
      }
    }
  }
}
```

### Feature Flags

Enable or disable features using environment variables:

```bash
# Feature flags
NEXT_PUBLIC_ENABLE_GITHUB_INTEGRATION="true"
NEXT_PUBLIC_ENABLE_AI_FEATURES="false"
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
```

## Environment-Specific Configuration

### Development

```bash
NODE_ENV="development"
NEXT_PUBLIC_DEBUG="true"
```

### Production

```bash
NODE_ENV="production"
NEXT_PUBLIC_DEBUG="false"
```

### Testing

```bash
NODE_ENV="test"
DATABASE_URL="postgresql://test:test@localhost:5432/shipsaas_test"
```

## Configuration Validation

ShipSaaS includes built-in configuration validation to ensure all required environment variables are properly set. Missing or invalid configurations will result in helpful error messages during startup.

## Next Steps

- [Database Configuration](/docs/configuration/database)
- [Authentication Configuration](/docs/configuration/authentication)
- [Payment Configuration](/docs/configuration/payments)
- [Email Configuration](/docs/configuration/email)
- [GitHub Integration](/docs/configuration/github-integration)
