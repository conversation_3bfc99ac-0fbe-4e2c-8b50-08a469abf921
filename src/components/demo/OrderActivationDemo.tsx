"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2, CreditCard, Calendar, DollarSign } from "lucide-react";
import { format } from "date-fns";

/**
 * Order Activation Demo Component
 * 订单激活演示组件
 */
export function OrderActivationDemo() {
  const [activatingOrders, setActivatingOrders] = useState<Set<string>>(new Set());

  // Mock order data / 模拟订单数据
  const mockOrders = [
    {
      id: 1,
      orderNo: "ORD-2024-001",
      amount: 2999, // $29.99
      status: "paid",
      createdAt: "2024-01-15T10:30:00Z",
      productName: "Pro Plan - Monthly",
      currency: "USD",
      paidAt: "2024-01-15T10:35:00Z",
    },
    {
      id: 2,
      orderNo: "ORD-2024-002",
      amount: 9999, // $99.99
      status: "activated",
      createdAt: "2024-01-10T14:20:00Z",
      productName: "Enterprise Plan - Annual",
      currency: "USD",
      paidAt: "2024-01-10T14:25:00Z",
    },
    {
      id: 3,
      orderNo: "ORD-2024-003",
      amount: 1999, // $19.99
      status: "pending",
      createdAt: "2024-01-20T09:15:00Z",
      productName: "Basic Plan - Monthly",
      currency: "USD",
      paidAt: null,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-blue-500 hover:bg-blue-600";
      case "activated":
        return "bg-green-500 hover:bg-green-600";
      case "pending":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "failed":
        return "bg-red-500 hover:bg-red-600";
      case "expired":
        return "bg-gray-500 hover:bg-gray-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount / 100);
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      paid: "Paid",
      activated: "Activated",
      pending: "Pending",
      failed: "Failed",
      expired: "Expired",
    };
    return statusMap[status.toLowerCase()] || status;
  };

  const handleActivateOrder = async (orderNo: string) => {
    try {
      setActivatingOrders(prev => new Set(prev).add(orderNo));
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate successful activation
      alert(`Order ${orderNo} activated successfully!`);
      
    } catch (error) {
      console.error("Error activating order:", error);
      alert("Failed to activate order. Please try again.");
    } finally {
      setActivatingOrders(prev => {
        const newSet = new Set(prev);
        newSet.delete(orderNo);
        return newSet;
      });
    }
  };

  const canActivateOrder = (order: any) => {
    return order.status.toLowerCase() === 'paid' && order.paidAt;
  };

  const isOrderActivating = (orderNo: string) => {
    return activatingOrders.has(orderNo);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container mx-auto max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Order Activation Demo</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Demonstration of order activation functionality for paid orders
          </p>
        </div>

        <div className="grid gap-6">
          {mockOrders.map((order) => (
            <div
              key={order.orderNo}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                    {order.productName}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Order ID: {order.orderNo}
                  </p>
                </div>
                <Badge className={`${getStatusColor(order.status)} text-white border-0`}>
                  {getStatusText(order.status)}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm mb-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Amount</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatAmount(order.amount, order.currency)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">Order Date</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {format(new Date(order.createdAt), "PPP")}
                    </p>
                  </div>
                </div>
                {order.paidAt && (
                  <div className="flex items-center gap-2">
                    <CreditCard className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-gray-500 dark:text-gray-400">Paid Date</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {format(new Date(order.paidAt), "PPP")}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Activation Button for Paid Orders */}
              {canActivateOrder(order) && (
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>Payment confirmed on {format(new Date(order.paidAt!), "PPP")}</span>
                    </div>
                    <Button
                      onClick={() => handleActivateOrder(order.orderNo)}
                      disabled={isOrderActivating(order.orderNo)}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {isOrderActivating(order.orderNo) ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Activating...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Activate Order
                        </>
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Click to activate your order and unlock your purchased features.
                  </p>
                </div>
              )}

              {/* Already Activated Status */}
              {order.status.toLowerCase() === 'activated' && (
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                    <CheckCircle className="w-4 h-4" />
                    <span className="font-medium">Order Activated - Features Unlocked</span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
