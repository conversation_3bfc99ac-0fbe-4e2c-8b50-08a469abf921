import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import { baseOptions } from '@/app/layout.config';
import { source } from '@/lib/source';
import type { ReactNode } from 'react';

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <DocsLayout
      {...baseOptions}
      tree={source.pageTree}
      sidebar={{
        defaultOpenLevel: 0,
        banner: (
          <div className="flex items-center gap-2 rounded-md bg-gradient-to-r from-blue-500/10 to-purple-500/10 p-3 text-sm mx-0">
            <span className="text-blue-600 dark:text-blue-400">📚</span>
            <span>Welcome to ShipSaaS Documentation</span>
          </div>
        ),
        collapsible: true,
      }}
    >
      {children}
    </DocsLayout>
  );
}
