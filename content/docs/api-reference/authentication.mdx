---
title: Authentication API
description: API endpoints for user authentication and session management
---

# Authentication API

API endpoints for user authentication and session management

The Authentication API provides endpoints for user registration, login, logout, password reset, and session management.

## Base URL

```
/api/auth
```

## Endpoints

### User Registration

Register a new user account.

**Endpoint:** `POST /api/auth/signup`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "name": "<PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "emailVerified": false
    },
    "message": "Verification email sent"
  }
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "EMAIL_ALREADY_EXISTS",
    "message": "An account with this email already exists"
  }
}
```

### User Login

Authenticate a user with email and password.

**Endpoint:** `POST /api/auth/signin`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "user"
    },
    "session": {
      "expires": "2024-02-01T00:00:00.000Z"
    }
  }
}
```

### OAuth Login

Initiate OAuth login with supported providers.

**Endpoint:** `GET /api/auth/signin/[provider]`

**Supported Providers:**
- `google` - Google OAuth
- `github` - GitHub OAuth
- `discord` - Discord OAuth

**Example:**
```bash
GET /api/auth/signin/google
```

**Response:**
Redirects to OAuth provider authorization page.

### User Logout

End the current user session.

**Endpoint:** `POST /api/auth/signout`

**Response:**
```json
{
  "success": true,
  "message": "Successfully signed out"
}
```

### Get Current Session

Retrieve the current user session.

**Endpoint:** `GET /api/auth/session`

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "user",
      "emailVerified": true
    },
    "expires": "2024-02-01T00:00:00.000Z"
  }
}
```

**Unauthenticated Response:**
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHENTICATED",
    "message": "No active session"
  }
}
```

### Password Reset Request

Request a password reset email.

**Endpoint:** `POST /api/auth/reset-password`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

### Password Reset Confirmation

Reset password with a valid token.

**Endpoint:** `POST /api/auth/reset-password/confirm`

**Request Body:**
```json
{
  "token": "reset_token_123",
  "password": "newsecurepassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

### Email Verification

Verify user email with a verification token.

**Endpoint:** `GET /api/auth/verify-email`

**Query Parameters:**
- `token` - Email verification token

**Example:**
```bash
GET /api/auth/verify-email?token=verify_token_123
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

### Resend Verification Email

Resend email verification for the current user.

**Endpoint:** `POST /api/auth/resend-verification`

**Headers:**
```
Authorization: Bearer <session_token>
```

**Response:**
```json
{
  "success": true,
  "message": "Verification email sent"
}
```

## Error Codes

### Authentication Errors

- `INVALID_CREDENTIALS` - Email or password is incorrect
- `EMAIL_ALREADY_EXISTS` - Account with email already exists
- `EMAIL_NOT_VERIFIED` - Email address not verified
- `ACCOUNT_DISABLED` - User account is disabled
- `TOO_MANY_ATTEMPTS` - Too many login attempts

### Token Errors

- `INVALID_TOKEN` - Token is invalid or expired
- `TOKEN_EXPIRED` - Token has expired
- `TOKEN_ALREADY_USED` - Token has already been used

### Session Errors

- `UNAUTHENTICATED` - No valid session
- `SESSION_EXPIRED` - Session has expired
- `INVALID_SESSION` - Session is invalid

## Rate Limiting

Authentication endpoints are rate-limited to prevent abuse:

- **Login attempts**: 5 per minute per IP
- **Password reset**: 3 per hour per email
- **Email verification**: 5 per hour per user
- **Registration**: 10 per hour per IP

Rate limit headers:
```
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: **********
```

## Security Features

### CSRF Protection

All state-changing requests require CSRF tokens:

```javascript
// Get CSRF token
const csrfToken = await fetch('/api/auth/csrf').then(r => r.json());

// Include in requests
fetch('/api/auth/signin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRF-Token': csrfToken.csrfToken
  },
  body: JSON.stringify({ email, password })
});
```

### Password Requirements

Passwords must meet the following requirements:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Session Security

- Sessions expire after 30 days of inactivity
- Secure, HTTP-only cookies
- CSRF protection enabled
- Session rotation on login

## Client-Side Usage

### React Hook

Use the `useSession` hook for client-side authentication:

```typescript
import { useSession } from 'next-auth/react';

function Profile() {
  const { data: session, status } = useSession();

  if (status === 'loading') return <p>Loading...</p>;
  if (status === 'unauthenticated') return <p>Access Denied</p>;

  return (
    <div>
      <h1>Welcome {session.user.name}</h1>
      <p>Email: {session.user.email}</p>
    </div>
  );
}
```

### Server-Side Usage

Check authentication on the server:

```typescript
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth.config';

export async function GET() {
  const session = await getServerSession(authConfig);
  
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }
  
  return Response.json({ user: session.user });
}
```

## Examples

### Complete Login Flow

```typescript
// Login function
async function login(email: string, password: string) {
  try {
    const response = await fetch('/api/auth/signin', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    const data = await response.json();

    if (data.success) {
      // Redirect to dashboard
      window.location.href = '/dashboard';
    } else {
      // Handle error
      console.error('Login failed:', data.error.message);
    }
  } catch (error) {
    console.error('Login error:', error);
  }
}
```

### Password Reset Flow

```typescript
// Request password reset
async function requestPasswordReset(email: string) {
  const response = await fetch('/api/auth/reset-password', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email })
  });

  const data = await response.json();
  return data;
}

// Confirm password reset
async function confirmPasswordReset(token: string, password: string) {
  const response = await fetch('/api/auth/reset-password/confirm', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token, password })
  });

  const data = await response.json();
  return data;
}
```
