---
title: Payment System
description: Complete payment processing with Stripe integration, subscriptions, and billing management
---

# Payment System

ShipSaaS includes a comprehensive payment system built with Stripe, providing secure payment processing, subscription management, and billing automation.

## Features

### Core Payment Features
- **Subscription Management** - Recurring billing with multiple tiers
- **One-time Payments** - Single purchase transactions
- **Customer Portal** - Self-service billing management
- **Invoice Generation** - Automated invoice creation
- **Tax Calculation** - Automatic tax handling
- **Payment Methods** - Credit cards, bank transfers, digital wallets

### Subscription Features
- **Multiple Pricing Tiers** - Starter, Standard, Premium plans
- **Proration** - Automatic proration for plan changes
- **Trial Periods** - Free trial support
- **Discounts & Coupons** - Promotional pricing
- **Usage-based Billing** - Metered billing support
- **Dunning Management** - Failed payment handling

## Configuration

### Stripe Setup

Configure your Stripe integration in the environment variables:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_PUBLISHABLE_KEY="pk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Product Configuration
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID="price_starter"
NEXT_PUBLIC_STRIPE_STANDARD_PRICE_ID="price_standard"
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID="price_premium"
```

### Pricing Configuration

Define your pricing tiers in the configuration:

```typescript
// src/lib/pricing.ts
export const pricingPlans = [
  {
    id: 'starter',
    name: 'Starter',
    price: 29,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID,
    features: [
      'Basic features',
      'Email support',
      '1 GitHub repository'
    ]
  },
  {
    id: 'standard',
    name: 'Standard',
    price: 99,
    priceId: process.env.NEXT_PUBLIC_STRIPE_STANDARD_PRICE_ID,
    features: [
      'All Starter features',
      'Priority support',
      '5 GitHub repositories',
      'Advanced analytics'
    ]
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 299,
    priceId: process.env.NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID,
    features: [
      'All Standard features',
      'Phone support',
      'Unlimited repositories',
      'Custom integrations'
    ]
  }
];
```

## Implementation

### Checkout Flow

Create a checkout session for subscription:

```typescript
// Create checkout session
export async function createCheckoutSession(priceId: string, customerId?: string) {
  const session = await stripe.checkout.sessions.create({
    mode: 'subscription',
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?success=true`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?canceled=true`,
    customer: customerId,
    allow_promotion_codes: true,
    billing_address_collection: 'required',
    tax_id_collection: {
      enabled: true,
    },
  });

  return session;
}
```

### Customer Portal

Provide self-service billing management:

```typescript
// Create customer portal session
export async function createPortalSession(customerId: string) {
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing`,
  });

  return session;
}
```

### Webhook Handling

Handle Stripe webhooks for real-time updates:

```typescript
// Webhook handler
export async function POST(request: Request) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature!,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    return new Response('Webhook signature verification failed', { status: 400 });
  }

  switch (event.type) {
    case 'customer.subscription.created':
      await handleSubscriptionCreated(event.data.object);
      break;
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object);
      break;
    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object);
      break;
    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object);
      break;
  }

  return new Response('Webhook handled', { status: 200 });
}
```

## Usage Examples

### Client-Side Integration

```typescript
// Pricing component
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

export function PricingCard({ plan }: { plan: PricingPlan }) {
  const handleSubscribe = async () => {
    const response = await fetch('/api/payments/create-checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ priceId: plan.priceId })
    });

    const { sessionId } = await response.json();
    const stripe = await stripePromise;
    
    await stripe?.redirectToCheckout({ sessionId });
  };

  return (
    <div className="pricing-card">
      <h3>{plan.name}</h3>
      <p>${plan.price}/month</p>
      <button onClick={handleSubscribe}>
        Subscribe
      </button>
    </div>
  );
}
```

### Subscription Status

Check user subscription status:

```typescript
// Get user subscription
export async function getUserSubscription(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { subscription: true }
  });

  if (!user?.subscription?.stripeSubscriptionId) {
    return null;
  }

  const subscription = await stripe.subscriptions.retrieve(
    user.subscription.stripeSubscriptionId
  );

  return {
    ...subscription,
    plan: getPlanFromPriceId(subscription.items.data[0].price.id)
  };
}
```

### Usage Tracking

Track usage for metered billing:

```typescript
// Record usage
export async function recordUsage(subscriptionId: string, quantity: number) {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  const usageRecord = await stripe.subscriptionItems.createUsageRecord(
    subscription.items.data[0].id,
    {
      quantity,
      timestamp: Math.floor(Date.now() / 1000),
      action: 'increment',
    }
  );

  return usageRecord;
}
```

## Advanced Features

### Proration Handling

Handle plan changes with proration:

```typescript
// Upgrade/downgrade subscription
export async function changeSubscription(subscriptionId: string, newPriceId: string) {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  
  const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
    items: [{
      id: subscription.items.data[0].id,
      price: newPriceId,
    }],
    proration_behavior: 'create_prorations',
  });

  return updatedSubscription;
}
```

### Discount Management

Apply discounts and coupons:

```typescript
// Apply coupon to subscription
export async function applyCoupon(subscriptionId: string, couponId: string) {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    coupon: couponId,
  });

  return subscription;
}
```

### Tax Handling

Configure automatic tax calculation:

```typescript
// Create checkout with tax calculation
const session = await stripe.checkout.sessions.create({
  mode: 'subscription',
  line_items: [{ price: priceId, quantity: 1 }],
  automatic_tax: { enabled: true },
  customer_update: {
    address: 'auto',
  },
  // ... other options
});
```

## Security Best Practices

### Webhook Security

Verify webhook signatures:

```typescript
// Verify webhook signature
function verifyWebhookSignature(body: string, signature: string) {
  try {
    return stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    throw new Error('Invalid webhook signature');
  }
}
```

### Payment Security

- Use HTTPS for all payment-related requests
- Validate all payment data on the server
- Never store sensitive payment information
- Implement proper error handling
- Use Stripe's secure payment forms

## Troubleshooting

### Common Issues

**Webhook Failures**
```bash
# Test webhook endpoint
curl -X POST https://yourdomain.com/api/stripe/webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"type": "test.event"}'
```

**Payment Failures**
```typescript
// Handle payment errors
try {
  const paymentIntent = await stripe.paymentIntents.create({
    amount: 2000,
    currency: 'usd',
  });
} catch (error) {
  if (error.type === 'StripeCardError') {
    // Card was declined
    console.log('Card declined:', error.message);
  } else {
    // Other error
    console.log('Payment error:', error.message);
  }
}
```

**Subscription Issues**
```typescript
// Debug subscription status
const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
  expand: ['latest_invoice', 'customer']
});

console.log('Subscription status:', subscription.status);
console.log('Current period:', subscription.current_period_start, subscription.current_period_end);
```

For more payment details, see the [Payments API Reference](/docs/api-reference/payments).
