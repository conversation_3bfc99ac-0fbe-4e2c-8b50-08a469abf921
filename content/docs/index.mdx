---
title: Introduction
description: Introducing ShipSaaS, a complete Next.js boilerplate for building SaaS websites
---

# Introduction

Introducing ShipSaaS, **a complete boilerplate for building SaaS websites**. This project is designed to accelerate your development with a robust, modular, and extensible foundation. Feel free to use it as is or extend it for your own needs.

People use ShipSaaS to build SaaS websites for a variety of reasons:

- 🚀 **Fast & Modern** - Built with Next.js 15 App Router and TypeScript
- 🎨 **Beautiful UI** - Powered by Tailwind CSS and Radix UI components
- 📱 **Responsive** - Works great on all devices
- 🔍 **SEO Optimized** - Built-in SEO features
- 💳 **Payment Ready** - Stripe integration with subscription management
- 🔐 **Secure Auth** - Complete authentication system with NextAuth.js
- 📧 **Email System** - Transactional emails with Resend
- 🔗 **GitHub Integration** - Automated repository access management

Whether you're a beginner or an experienced developer, this documentation will guide you through the setup, configuration, and deployment of your SaaS website.

This guide assumes you have some familiarity with Next.js and modern web development. If you don't have any of these, please refer to the [Official Documentation](#official-documentation) as we will not cover the basics in this guide.

Let's get started!

## What is ShipSaaS?

Welcome to ShipSaaS, **a complete boilerplate for building SaaS websites**. This project is designed to accelerate your development with a robust, modular, and extensible foundation. Feel free to use it as is or extend it for your own needs.

## Tech Stack

### Next.js 15
The React framework for building full-stack web applications using React 19.

### Prisma ORM
An ORM tool that helps you access your database in a type-safe way.

### Tailwind CSS
A utility-first CSS framework packed with utility classes that can be composed to build any design.

### Radix UI / Shadcn/ui
A collection of pre-built components and blocks that you can use to build your SaaS websites.

### NextAuth.js
A complete authentication solution for Next.js applications with multiple providers.

### Resend
A modern email service that allows you to send emails from your application.

### Fumadocs
A beautiful documentation framework for Developers, flexible and performant.

### Stripe
The most popular and secure payment processor for SaaS websites.

### Zustand
A small, fast and scalable state management solution for React applications.

### Next-Intl
Internationalization library for Next.js that supports routing, messages, and localization.

## Scope of This Documentation

This documentation will guide you through configuring, running, and deploying the boilerplate, and will provide links to the official documentation of the underlying technologies where necessary. To fully grasp the boilerplate's capabilities, it's essential to understand these technologies, so be sure to explore their documentation as well.

For anything strictly related to the ShipSaaS boilerplate, this documentation has you covered!

### Official Documentation

For in-depth understanding of the underlying technologies, refer to their official documentation:

- Next.js: [Next.js Documentation](https://nextjs.org/docs)
- Prisma: [Prisma Documentation](https://www.prisma.io/docs)
- Tailwind CSS: [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- Radix UI: [Radix UI Documentation](https://www.radix-ui.com/primitives/docs)
- Shadcn/ui: [Shadcn/ui Documentation](https://ui.shadcn.com/docs)
- NextAuth.js: [NextAuth.js Documentation](https://next-auth.js.org/getting-started/introduction)
- Resend: [Resend Documentation](https://resend.com/docs)
- Fumadocs: [Fumadocs Documentation](https://fumadocs.vercel.app/docs)
- Stripe: [Stripe Documentation](https://stripe.com/docs)
- Zustand: [Zustand Documentation](https://zustand-demo.pmnd.rs/)
- Next-Intl: [Next-Intl Documentation](https://next-intl.dev/)

Understanding these technologies is crucial for building a successful SaaS website.

## Getting Help

If you're stuck, here are some ways to get help:

- Check the [ShipSaaS documentation](/docs)
- Open an issue on [GitHub Issues](https://github.com/wenhaofree/shipsaas-office/issues)
- Open a discussion on [GitHub Discussions](https://github.com/wenhaofree/shipsaas-office/discussions)
- Contact our support team

## Next Steps

Now that you have ShipSaaS running, here are some next steps:

### Getting Started
Learn how to set up and run your ShipSaaS website

### Environment Setup
Configure environment variables

### Database Setup
Set up and configure your database connection

### IDE Setup
Configure your development environment

Ready to get started? Check out our [Getting Started Guide](/docs/getting-started/introduction)!
