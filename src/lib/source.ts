import { docs, meta } from '../../.source';
import { createMDXSource } from 'fumadocs-mdx';
import { loader } from 'fumadocs-core/source';
import type { InferMetaType, InferPageType } from 'fumadocs-core/source';

// Main documentation source
export const source = loader({
  baseUrl: '/docs',
  source: createMDXSource(docs, meta),
});

// Type exports for better TypeScript support
export type DocsPage = InferPageType<typeof source>;
export type DocsMeta = InferMetaType<typeof source>;
