---
title: GitHub Integration
description: Automated GitHub repository access management based on subscription tiers
---

# GitHub Integration

ShipSaaS includes powerful GitHub integration that automatically manages repository access based on user subscription tiers, providing seamless access control for your SaaS products.

## Features

### Core Integration Features
- **Automatic Invitations** - Send repository invites based on subscription
- **Tier-based Access** - Different repositories for different subscription tiers
- **Access Revocation** - Remove access when subscriptions end
- **Status Tracking** - Monitor invitation and access status
- **Bulk Management** - Handle multiple users and repositories

### Repository Management
- **Multiple Repositories** - Support for different repo tiers
- **Permission Levels** - Read, write, or admin access
- **Organization Support** - Work with GitHub organizations
- **Team Management** - Assign users to specific teams

## Configuration

### GitHub API Setup

Configure GitHub API access:

```bash
# GitHub Configuration
GITHUB_TOKEN="ghp_xxxxxxxxxxxxxxxxxxxx"

# Repository Configuration
GITHUB_STARTER_REPO="your-org/starter-template"
GITHUB_STANDARD_REPO="your-org/standard-template"
GITHUB_PREMIUM_REPO="your-org/premium-template"

# Organization (optional)
GITHUB_ORG="your-organization"
```

### Repository Mapping

Define repository access by subscription tier:

```typescript
// src/lib/github-config.ts
export const repositoryConfig = {
  starter: {
    repo: process.env.GITHUB_STARTER_REPO!,
    permission: 'pull' as const,
    description: 'Starter template access'
  },
  standard: {
    repo: process.env.GITHUB_STANDARD_REPO!,
    permission: 'pull' as const,
    description: 'Standard template access'
  },
  premium: {
    repo: process.env.GITHUB_PREMIUM_REPO!,
    permission: 'push' as const,
    description: 'Premium template with push access'
  }
};
```

## Implementation

### GitHub API Client

Set up the GitHub API client:

```typescript
// src/lib/github.ts
import { Octokit } from '@octokit/rest';

export const github = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

export async function inviteUserToRepository(
  username: string,
  repository: string,
  permission: 'pull' | 'push' | 'admin' = 'pull'
) {
  const [owner, repo] = repository.split('/');
  
  try {
    const invitation = await github.repos.addCollaborator({
      owner,
      repo,
      username,
      permission,
    });

    return {
      success: true,
      invitationId: invitation.data.id,
      url: invitation.data.html_url
    };
  } catch (error) {
    console.error('GitHub invitation error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
```

### Subscription-based Access

Automatically manage access based on subscriptions:

```typescript
// Handle subscription activation
export async function handleSubscriptionActivated(subscription: Subscription) {
  const user = await prisma.user.findUnique({
    where: { id: subscription.userId }
  });

  if (!user?.githubUsername) {
    console.log('User has no GitHub username');
    return;
  }

  const config = repositoryConfig[subscription.tier];
  if (!config) {
    console.log('No repository config for tier:', subscription.tier);
    return;
  }

  const result = await inviteUserToRepository(
    user.githubUsername,
    config.repo,
    config.permission
  );

  if (result.success) {
    await prisma.githubInvitation.create({
      data: {
        userId: user.id,
        repository: config.repo,
        invitationId: result.invitationId,
        status: 'pending',
        tier: subscription.tier
      }
    });

    // Send notification email
    await sendGitHubInvitationEmail(user.email, {
      repository: config.repo,
      invitationUrl: result.url
    });
  }
}
```

### Access Revocation

Remove access when subscriptions end:

```typescript
// Handle subscription cancellation
export async function handleSubscriptionCancelled(subscription: Subscription) {
  const user = await prisma.user.findUnique({
    where: { id: subscription.userId },
    include: { githubInvitations: true }
  });

  if (!user?.githubUsername) return;

  // Remove access from all repositories for this tier
  const invitations = user.githubInvitations.filter(
    inv => inv.tier === subscription.tier && inv.status === 'accepted'
  );

  for (const invitation of invitations) {
    await removeUserFromRepository(user.githubUsername, invitation.repository);
    
    await prisma.githubInvitation.update({
      where: { id: invitation.id },
      data: { status: 'revoked' }
    });
  }
}

export async function removeUserFromRepository(username: string, repository: string) {
  const [owner, repo] = repository.split('/');
  
  try {
    await github.repos.removeCollaborator({
      owner,
      repo,
      username,
    });
    return { success: true };
  } catch (error) {
    console.error('GitHub removal error:', error);
    return { success: false, error: error.message };
  }
}
```

## Usage Examples

### Manual Invitation

Send manual invitations through the admin interface:

```typescript
// API route for manual invitations
export async function POST(request: Request) {
  const { userId, tier } = await request.json();
  
  const user = await prisma.user.findUnique({
    where: { id: userId }
  });

  if (!user?.githubUsername) {
    return Response.json(
      { error: 'User has no GitHub username' },
      { status: 400 }
    );
  }

  const config = repositoryConfig[tier];
  const result = await inviteUserToRepository(
    user.githubUsername,
    config.repo,
    config.permission
  );

  if (result.success) {
    return Response.json({
      message: 'Invitation sent successfully',
      invitationUrl: result.url
    });
  } else {
    return Response.json(
      { error: result.error },
      { status: 500 }
    );
  }
}
```

### Invitation Status Check

Check the status of GitHub invitations:

```typescript
// Check invitation status
export async function checkInvitationStatus(invitationId: number, repository: string) {
  const [owner, repo] = repository.split('/');
  
  try {
    const invitation = await github.repos.getInvitation({
      owner,
      repo,
      invitation_id: invitationId,
    });

    return {
      status: invitation.data.state, // 'pending' or 'accepted'
      invitee: invitation.data.invitee?.login,
      createdAt: invitation.data.created_at
    };
  } catch (error) {
    if (error.status === 404) {
      return { status: 'not_found' };
    }
    throw error;
  }
}
```

### Bulk Operations

Handle multiple users or repositories:

```typescript
// Bulk invite users
export async function bulkInviteUsers(users: Array<{ username: string; tier: string }>) {
  const results = [];
  
  for (const user of users) {
    const config = repositoryConfig[user.tier];
    if (!config) continue;
    
    const result = await inviteUserToRepository(
      user.username,
      config.repo,
      config.permission
    );
    
    results.push({
      username: user.username,
      repository: config.repo,
      success: result.success,
      error: result.error
    });
    
    // Rate limiting - GitHub API has limits
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return results;
}
```

## Advanced Features

### Webhook Integration

Handle GitHub webhooks for real-time updates:

```typescript
// GitHub webhook handler
export async function POST(request: Request) {
  const payload = await request.json();
  const event = request.headers.get('x-github-event');
  
  switch (event) {
    case 'repository_invitation':
      await handleRepositoryInvitation(payload);
      break;
    case 'member':
      await handleMemberEvent(payload);
      break;
  }
  
  return new Response('OK');
}

async function handleRepositoryInvitation(payload: any) {
  if (payload.action === 'accepted') {
    await prisma.githubInvitation.updateMany({
      where: {
        invitationId: payload.invitation.id,
        status: 'pending'
      },
      data: { status: 'accepted' }
    });
  }
}
```

### Team Management

Assign users to GitHub teams:

```typescript
// Add user to team
export async function addUserToTeam(username: string, teamSlug: string, org: string) {
  try {
    await github.teams.addOrUpdateMembershipForUserInOrg({
      org,
      team_slug: teamSlug,
      username,
      role: 'member'
    });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### Repository Analytics

Track repository access and usage:

```typescript
// Get repository statistics
export async function getRepositoryStats(repository: string) {
  const [owner, repo] = repository.split('/');
  
  const [repoData, collaborators, traffic] = await Promise.all([
    github.repos.get({ owner, repo }),
    github.repos.listCollaborators({ owner, repo }),
    github.repos.getViews({ owner, repo }).catch(() => null)
  ]);
  
  return {
    name: repoData.data.name,
    description: repoData.data.description,
    stars: repoData.data.stargazers_count,
    forks: repoData.data.forks_count,
    collaborators: collaborators.data.length,
    views: traffic?.data.count || 0
  };
}
```

## Security Best Practices

### Token Security

- Use fine-grained personal access tokens
- Limit token permissions to necessary scopes
- Rotate tokens regularly
- Store tokens securely in environment variables

### Access Control

- Implement proper permission levels
- Audit repository access regularly
- Monitor invitation acceptance rates
- Log all access changes

## Troubleshooting

### Common Issues

**Authentication Errors**
```typescript
// Test GitHub API connection
export async function testGitHubConnection() {
  try {
    const user = await github.users.getAuthenticated();
    return {
      success: true,
      user: user.data.login,
      scopes: user.headers['x-oauth-scopes']
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
```

**Rate Limiting**
```typescript
// Handle rate limits
export async function makeGitHubRequest(requestFn: () => Promise<any>) {
  try {
    return await requestFn();
  } catch (error) {
    if (error.status === 403 && error.message.includes('rate limit')) {
      const resetTime = error.response.headers['x-ratelimit-reset'];
      const waitTime = (resetTime * 1000) - Date.now();
      
      console.log(`Rate limited. Waiting ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      
      return await requestFn();
    }
    throw error;
  }
}
```

**Repository Access Issues**
```typescript
// Verify repository access
export async function verifyRepositoryAccess(repository: string) {
  const [owner, repo] = repository.split('/');
  
  try {
    await github.repos.get({ owner, repo });
    return { accessible: true };
  } catch (error) {
    return {
      accessible: false,
      error: error.status === 404 ? 'Repository not found' : error.message
    };
  }
}
```

For more GitHub integration details, see the [GitHub API Reference](/docs/api-reference/github).
