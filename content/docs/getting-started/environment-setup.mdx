---
title: Environment Setup
description: Configure environment variables for your ShipSaaS application
---

# Environment Setup

Configure environment variables for your ShipSaaS application

ShipSaaS uses environment variables to configure the project. You can set them in the `.env` file, so first copy the `env.example` file to `.env` to have a starting point.

```bash
cp env.example .env
```

Then, open the `.env` file and set the variables to your desired values. You can find more information about the environment variables below.

## Required Environment Variables

### Database Configuration

ShipSaaS requires a PostgreSQL database to work. Before creating your project, make sure to have created a new database and have the connection string ready.

```bash
# Database URL - PostgreSQL connection string
DATABASE_URL="postgresql://user:password@host:port/database"
```

The PostgreSQL connection string will look something like this:

```bash
postgresql://user:password@host:port/database
```

We've covered some options for hosting your PostgreSQL database:

- **Supabase** (recommended) - Free tier available
- **Neon** - Serverless PostgreSQL
- **PlanetScale** - MySQL alternative
- **Railway** - Simple deployment platform

### Authentication Configuration

```bash
# NextAuth.js configuration
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# GitHub OAuth (optional)
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

### Payment Configuration

```bash
# Stripe configuration
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Stripe pricing configuration
NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID="price_..."
NEXT_PUBLIC_STRIPE_STANDARD_PRICE_ID="price_..."
NEXT_PUBLIC_STRIPE_PREMIUM_PRICE_ID="price_..."
```

### Email Configuration

```bash
# Resend email service
RESEND_API_KEY="re_..."
RESEND_FROM_EMAIL="<EMAIL>"
```

### GitHub Integration

```bash
# GitHub API token for repository management
GITHUB_TOKEN="ghp_..."
GITHUB_STARTER_REPO="your-org/starter-repo"
GITHUB_STANDARD_REPO="your-org/standard-repo"
GITHUB_PREMIUM_REPO="your-org/premium-repo"
```

### Application Configuration

```bash
# Application URL
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Application name and metadata
NEXT_PUBLIC_APP_NAME="ShipSaaS"
NEXT_PUBLIC_APP_DESCRIPTION="Your SaaS Starter Kit"
```

## Optional Environment Variables

### Analytics

```bash
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY="phc_..."
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"
```

### Development

```bash
# Development mode settings
NODE_ENV="development"
NEXT_PUBLIC_DEBUG="true"
```

## Environment Variable Security

### Production Security

- Never commit `.env` files to version control
- Use different values for development and production
- Rotate secrets regularly
- Use environment-specific configurations

### Secret Management

For production deployments, use secure secret management:

- **Vercel**: Environment Variables in dashboard
- **Cloudflare Pages**: Environment Variables in dashboard
- **Railway**: Environment Variables in project settings
- **Docker**: Use Docker secrets or environment files

## Validation

ShipSaaS includes environment variable validation to ensure all required variables are set correctly. If any required variables are missing, the application will show helpful error messages.

## Next Steps

After setting up your environment variables:

1. [Initialize the database](/docs/getting-started/installation#initialize-the-database)
2. [Start the development server](/docs/getting-started/installation#start-development-server)
3. [Configure external services](/docs/configuration)

For more detailed configuration options, see the [Configuration Guide](/docs/configuration).
