import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { routing } from './i18n/routing';
import { defaultLocale } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);

export default function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files, API routes, docs, and special Next.js files
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/docs') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon') ||
    pathname === '/favicon.ico' ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml' ||
    pathname === '/manifest.json' ||
    pathname.startsWith('/images/') ||
    pathname.startsWith('/icons/')
  ) {
    return NextResponse.next();
  }

  // 检查是否访问了默认语言的带前缀路径，如果是则重定向到无前缀路径
  if (pathname.startsWith(`/${defaultLocale}/`)) {
    const newPathname = pathname.replace(`/${defaultLocale}`, '') || '/';
    const url = new URL(newPathname, request.url);

    // 保留所有查询参数
    request.nextUrl.searchParams.forEach((value, key) => {
      url.searchParams.set(key, value);
    });

    return NextResponse.redirect(url, 301);
  }

  // 处理 NextAuth 的登录页面路径重定向
  // NextAuth 配置中使用 /auth/signin，但我们的页面在 [locale]/auth/signin
  if (pathname === '/auth/signin') {
    // 让 next-intl 中间件处理这个请求，它会自动添加适当的 locale 前缀
    // 对于默认语言，next-intl 会重写到 /en/auth/signin（内部），但 URL 保持 /auth/signin
    // 对于其他语言，用户应该通过正确的 locale 路径访问
  }

  return intlMiddleware(request);
}

export const config = {
  // Match all paths except static files, API routes, and Next.js internals
  matcher: [
    '/((?!api|_next/static|_next/image|favicon|.*\\..*|robots\\.txt|sitemap\\.xml|manifest\\.json).*)'
  ]
};