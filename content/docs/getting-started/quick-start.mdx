---
title: Quick Start
description: Get up and running with ShipSaaS in minutes
---

# Quick Start Guide

This guide will help you get ShipSaaS up and running quickly and show you how to customize it for your SaaS product.

## 5-Minute Setup

### 1. <PERSON><PERSON> and Install

```bash
git clone https://github.com/your-username/shipsaas-office.git
cd shipsaas-office
pnpm install
```

### 2. Quick Environment Setup

Create `.env.local` with minimal configuration:

```bash
# Database (use Supabase for quick setup)
DATABASE_URL="postgresql://..."

# NextAuth
NEXTAUTH_SECRET="your-random-secret"
NEXTAUTH_URL="http://localhost:3000"

# Stripe (use test keys)
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."

# App URL
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 3. Initialize Database

```bash
pnpm db:push
```

### 4. Start Development

```bash
pnpm dev
```

Visit `http://localhost:3000` - you should see the ShipSaaS homepage!

## First Customizations

### Update Branding

1. **App Name**: Edit `src/constants/app.ts`
   ```typescript
   export const APP_CONFIG = {
     name: "Your SaaS Name",
     description: "Your SaaS description",
     // ...
   }
   ```

2. **Logo**: Replace files in `public/`
   - `logo.png` - Main logo
   - `favicon.ico` - Favicon

3. **Colors**: Update `tailwind.config.ts`
   ```typescript
   theme: {
     extend: {
       colors: {
         primary: {
           // Your brand colors
         }
       }
     }
   }
   ```

### Configure Pricing

1. **Stripe Products**: Create products in Stripe dashboard
2. **Update Pricing**: Edit `src/constants/pricing.ts`
   ```typescript
   export const PRICING_PLANS = [
     {
       name: "Starter",
       price: 29,
       stripePriceId: "price_...",
       features: ["Feature 1", "Feature 2"]
     }
   ]
   ```

### Set Up Authentication

1. **Email Provider**: Configure in `src/auth.config.ts`
2. **OAuth Providers**: Add Google, GitHub, etc.
3. **Custom Pages**: Modify auth pages in `src/app/[locale]/auth/`

## Key Concepts

### Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── [locale]/       # Internationalized routes
│   ├── api/            # API routes
│   └── globals.css     # Global styles
├── components/         # Reusable components
├── lib/               # Utility functions
├── hooks/             # Custom React hooks
├── types/             # TypeScript types
└── constants/         # App configuration
```

### Database Schema

Key models in `prisma/schema.prisma`:

- **User** - User accounts and profiles
- **Order** - Payment and subscription data
- **Account** - OAuth account linking
- **Session** - User sessions

### API Routes

Main API endpoints:

- `/api/auth/*` - Authentication (NextAuth.js)
- `/api/stripe/*` - Payment processing
- `/api/orders/*` - Order management
- `/api/users/*` - User management
- `/api/github/*` - GitHub integration

## Common Tasks

### Adding a New Feature

1. **Create Components**: Add to `src/components/`
2. **Add API Routes**: Create in `src/app/api/`
3. **Update Database**: Modify `prisma/schema.prisma`
4. **Add Pages**: Create in `src/app/[locale]/`

### Customizing UI

1. **Components**: Extend existing components
2. **Styling**: Use Tailwind CSS classes
3. **Themes**: Modify theme configuration
4. **Icons**: Use Lucide React icons

### Adding Integrations

1. **Install Package**: `pnpm add package-name`
2. **Environment Variables**: Add to `.env.local`
3. **API Integration**: Create service in `src/lib/`
4. **UI Integration**: Add to relevant components

## Testing Your Changes

### Development Testing

```bash
# Run development server
pnpm dev

# Run linting
pnpm lint

# Run type checking
pnpm type-check
```

### Database Testing

```bash
# View database
pnpm db:studio

# Reset database
pnpm db:reset

# Run migrations
pnpm db:push
```

### Payment Testing

Use Stripe test cards:
- **Success**: `4242 4242 4242 4242`
- **Decline**: `4000 0000 0000 0002`
- **3D Secure**: `4000 0025 0000 3155`

## Deployment Preview

### Quick Deploy to Vercel

1. **Connect Repository**: Import to Vercel
2. **Environment Variables**: Add production values
3. **Database**: Use production database URL
4. **Deploy**: Automatic deployment on push

### Environment Variables for Production

```bash
# Production Database
DATABASE_URL="postgresql://prod-url"

# Production Stripe
STRIPE_SECRET_KEY="sk_live_..."
STRIPE_PUBLISHABLE_KEY="pk_live_..."

# Production Domain
NEXT_PUBLIC_APP_URL="https://yourdomain.com"
NEXTAUTH_URL="https://yourdomain.com"
```

## Next Steps

Now that you have ShipSaaS running:

1. **Explore Features** - Check out the [Features](/docs/features) section
2. **API Reference** - Learn about the [API](/docs/api) endpoints
3. **Deployment** - Read the [Deployment](/docs/deployment) guides
4. **Customization** - Dive into [Advanced](/docs/advanced) topics

## Getting Help

- 📖 **Documentation** - Browse all sections
- 🐛 **Issues** - Report bugs on GitHub
- 💬 **Discussions** - Join community discussions
- 📧 **Support** - Contact support team

Ready to build your SaaS? Let's explore the [Features](/docs/features/authentication) in detail!
