# 密码重置功能配置指南

本文档介绍如何配置和使用密码重置功能。

## 功能概述

密码重置功能允许用户通过邮箱重置忘记的密码，包含以下流程：

1. 用户点击"忘记密码"
2. 输入邮箱地址
3. 系统生成唯一的重置token（1小时有效）
4. 发送包含重置链接的邮件到用户邮箱
5. 用户点击链接，验证token有效性
6. 用户设置新密码
7. Token标记为已使用，防止重复使用

## 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# Resend API密钥
RESEND_API_KEY=re_xxxxxxxxx

# 发件人邮箱地址
NEXT_PUBLIC_FROM_EMAIL=<EMAIL>
```

### 获取Resend API密钥

1. 访问 [Resend官网](https://resend.com)
2. 注册账户并登录
3. 在Dashboard中创建API密钥
4. 复制API密钥到环境变量

### 配置发件人邮箱

- 使用您拥有的域名邮箱
- 确保域名已在Resend中验证
- 建议使用 `noreply@` 前缀

## 数据库表结构

系统使用现有的 `users` 表，添加了以下字段：

```sql
ALTER TABLE users ADD COLUMN reset_token VARCHAR(255);
ALTER TABLE users ADD COLUMN reset_token_expires_at TIMESTAMPTZ;
```

这种设计的优势：
- 无需创建额外的表
- 简化了数据关系
- 减少了数据库查询复杂度
- 自动与用户数据保持一致性

## API端点

### 1. 忘记密码 - POST /api/auth/forgot-password

请求体：
```json
{
  "email": "<EMAIL>",
  "locale": "en" // 可选，默认为 "en"
}
```

响应：
```json
{
  "success": true,
  "message": "If the email exists in our system, you will receive a password reset link shortly."
}
```

### 2. 重置密码 - POST /api/auth/reset-password

请求体：
```json
{
  "token": "reset_token_here",
  "password": "new_password"
}
```

响应：
```json
{
  "success": true,
  "message": "Password has been reset successfully. You can now sign in with your new password."
}
```

## 页面路由

- 忘记密码表单：集成在登录页面中
- 重置密码页面：`/[locale]/auth/reset-password?token=xxx`

## 安全特性

1. **Token安全**：
   - 使用加密随机生成的64字符token
   - 1小时后自动过期
   - 一次性使用，使用后立即失效

2. **邮箱枚举防护**：
   - 无论邮箱是否存在都返回相同的成功消息
   - 防止攻击者枚举系统中的邮箱地址

3. **密码强度**：
   - 要求至少8个字符
   - 前端和后端双重验证

4. **数据清理**：
   - 自动清理过期的token
   - 使用后立即清除token
   - 防止token重复使用

## 国际化支持

支持中英文双语：

- 英文 (en)：完整的英文界面和邮件模板
- 中文 (zh)：完整的中文界面和邮件模板

邮件内容会根据用户选择的语言自动调整。

## 测试

### 开发环境测试

1. 访问 `/api/test-email` 检查邮件服务配置状态
2. 确保环境变量正确设置
3. 测试完整的密码重置流程

### 生产环境注意事项

1. 确保域名已在Resend中验证
2. 监控邮件发送状态和错误日志
3. 定期清理过期的重置token
4. 设置适当的邮件发送频率限制

## 故障排除

### 常见问题

1. **邮件发送失败**：
   - 检查RESEND_API_KEY是否正确
   - 确认发件人邮箱域名已验证
   - 查看服务器日志获取详细错误信息

2. **重置链接无效**：
   - 检查token是否已过期（1小时）
   - 确认token是否已被使用
   - 验证URL参数是否完整

3. **页面无法访问**：
   - 确认路由配置正确
   - 检查国际化设置
   - 验证组件导入路径

### 日志监控

系统会记录以下关键事件：
- 密码重置请求
- 邮件发送状态
- Token验证结果
- 密码重置成功/失败

建议在生产环境中监控这些日志以确保功能正常运行。
